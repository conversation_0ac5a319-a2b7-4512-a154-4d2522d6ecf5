const translation = {
  common: {
    undo: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    redo: '<PERSON><PERSON><PERSON><PERSON>',
    editing: '<PERSON><PERSON>jan<PERSON>',
    autoSaved: '<PERSON>odejn<PERSON> shranje<PERSON>',
    unpublished: 'Nepublicirano',
    published: '<PERSON>b<PERSON><PERSON><PERSON><PERSON><PERSON>',
    publish: 'Objavi',
    update: '<PERSON><PERSON><PERSON><PERSON>',
    run: '<PERSON>až<PERSON>',
    running: 'V teku',
    inRunMode: 'V načinu zagona',
    inPreview: 'V predogledu',
    inPreviewMode: 'V načinu predogleda',
    preview: 'Predogled',
    viewRunHistory: 'Ogled zgodovine zagona',
    runHistory: 'Zgodovina zagona',
    goBackToEdit: 'Nazaj na urejevalnik',
    conversationLog: 'Zapisnik pogovora',
    features: 'Značilnosti',
    debugAndPreview: 'Predogled',
    restart: 'Ponovni zagon',
    currentDraft: 'Trenutni osnutek',
    currentDraftUnpublished: 'Trenutni osnutek ni objavljen',
    latestPublished: '<PERSON><PERSON><PERSON><PERSON> objavlje<PERSON>',
    publishedAt: 'Objavlje<PERSON> ob',
    restore: 'Obnovi',
    runApp: 'Zaženi aplikacijo',
    batchRunApp: 'Serijski zagon aplikacije',
    accessAPIReference: 'Dostop do API referenc',
    embedIntoSite: 'Vdelaj v spletno stran',
    addTitle: 'Dodaj naslov...',
    addDescription: 'Dodaj opis...',
    noVar: 'Ni spremenljivke',
    searchVar: 'Išči spremenljivko',
    variableNamePlaceholder: 'Ime spremenljivke',
    setVarValuePlaceholder: 'Nastavi vrednost spremenljivke',
    needConnectTip: 'Ta korak ni povezan z ničemer',
    maxTreeDepth: 'Največja omejitev je {{depth}} vozlišč na vejo',
    needEndNode: 'Dodati je treba zaključni blok',
    needAnswerNode: 'Dodati je treba blok z odgovorom',
    workflowProcess: 'Proces delovnega toka',
    notRunning: 'Še ni v teku',
    previewPlaceholder: 'Vnesite vsebino v spodnje polje za začetek odpravljanja napak klepetalnika',
    effectVarConfirm: {
      title: 'Odstrani spremenljivko',
      content: 'Spremenljivka se uporablja v drugih vozliščih. Ali jo kljub temu želite odstraniti?',
    },
    insertVarTip: 'Pritisnite tipko \'/\' za hitro vstavljanje',
    processData: 'Obdelava podatkov',
    input: 'Vnos',
    output: 'Izhod',
    jinjaEditorPlaceholder: 'Vnesite \'/\' ali \'{\' za vstavljanje spremenljivke',
    viewOnly: 'Samo ogled',
    showRunHistory: 'Prikaži zgodovino zagona',
    enableJinja: 'Omogoči podporo za Jinja predloge',
    learnMore: 'Več informacij',
    copy: 'Kopiraj',
    duplicate: 'Podvoji',
    addBlock: 'Dodaj blok',
    pasteHere: 'Prilepi tukaj',
    pointerMode: 'Način kazalca',
    handMode: 'Način roke',
    model: 'Model',
    workflowAsTool: 'Potek dela kot orodje',
    configureRequired: 'Potrebna konfiguracija',
    configure: 'Konfiguriraj',
    manageInTools: 'Upravljaj v Orodjih',
    workflowAsToolTip: 'Po posodobitvi poteka dela je potrebno ponovno konfigurirati orodje.',
    viewDetailInTracingPanel: 'Oglejte si podrobnosti',
    syncingData: 'Sinhronizacija podatkov, le nekaj sekund.',
    importDSL: 'Uvozi DSL',
    importDSLTip: 'Trenutni osnutek bo prepisan. Pred uvozom izvozite delovni tok kot varnostno kopijo.',
    backupCurrentDraft: 'Varnostno kopiraj trenutni osnutek',
    chooseDSL: 'Izberite DSL(yml) datoteko',
    overwriteAndImport: 'Prepiši in uvozi',
    importFailure: 'Uvoz ni uspel',
    importSuccess: 'Uvoz uspešen',
    parallelRun: 'Vzporedni zagon',
    parallelTip: {
      click: {
        title: 'Klikni',
        desc: ' za dodajanje',
      },
      drag: {
        title: 'Povleci',
        desc: ' za povezavo',
      },
      limit: 'Vzporednost je omejena na {{num}} vej.',
      depthLimit: 'Omejitev gnezdenja vzporednih slojev na {{num}} slojev',
    },
    disconnect: 'Prekini povezavo',
    jumpToNode: 'Skoči na to vozlišče',
    addParallelNode: 'Dodaj vzporedno vozlišče',
    parallel: 'VZPOREDNO',
    branch: 'VEJA',
    fileUploadTip: 'Funkcije nalaganja slik so nadgrajene na nalaganje datotek.',
    featuresDocLink: 'Izvedi več',
    featuresDescription: 'Izboljšajte uporabniško izkušnjo spletne aplikacije',
    ImageUploadLegacyTip: 'Zdaj lahko ustvarite spremenljivke vrste datoteke v začetnem obrazcu. V prihodnje ne bomo več podpirali funkcije nalaganja slik.',
    importWarning: 'Previdnost',
    importWarningDetails: 'Razlika v različici DSL lahko vpliva na nekatere funkcije',
    openInExplore: 'Odpri v razišči',
    addFailureBranch: 'Dodajanje veje »Fail«',
    onFailure: 'O neuspehu',
    noHistory: 'Brez zgodovine',
    loadMore: 'Nalaganje več potekov dela',
    exportJPEG: 'Izvozi kot JPEG',
    exportPNG: 'Izvozi kot PNG',
    noExist: 'Nobena takšna spremenljivka ne obstaja.',
    exitVersions: 'Izhodne različice',
    versionHistory: 'Zgodovina različic',
    publishUpdate: 'Objavi posodobitev',
    exportSVG: 'Izvozi kot SVG',
    referenceVar: 'Referenčna spremenljivka',
    exportImage: 'Izvozi sliko',
  },
  env: {
    envPanelTitle: 'Spremenljivke okolja',
    envDescription: 'Spremenljivke okolja se uporabljajo za shranjevanje zasebnih informacij in poverilnic. So samo za branje in jih je mogoče ločiti od DSL datoteke med izvozom.',
    envPanelButton: 'Dodaj spremenljivko',
    modal: {
      title: 'Dodaj spremenljivko okolja',
      editTitle: 'Uredi spremenljivko okolja',
      type: 'Vrsta',
      name: 'Ime',
      namePlaceholder: 'ime okolja',
      value: 'Vrednost',
      valuePlaceholder: 'vrednost okolja',
      secretTip: 'Uporablja se za definiranje občutljivih informacij ali podatkov, s konfiguriranimi nastavitvami DSL za preprečevanje uhajanja.',
    },
    export: {
      title: 'Izvoziti skrivne spremenljivke okolja?',
      checkbox: 'Izvozi skrivne vrednosti',
      ignore: 'Izvozi DSL',
      export: 'Izvozi DSL z skrivnimi vrednostmi',
    },
    chatVariable: {
      panelTitle: 'Spremenljivke pogovora',
      panelDescription: 'Spremenljivke pogovora se uporabljajo za shranjevanje interaktivnih informacij, ki jih mora LLM zapomniti, vključno z zgodovino pogovorov, naloženimi datotekami, uporabniškimi nastavitvami. So za branje in pisanje.',
      docLink: 'Obiščite naše dokumente za več informacij.',
      button: 'Dodaj spremenljivko',
      modal: {
        title: 'Dodaj spremenljivko pogovora',
        editTitle: 'Uredi spremenljivko pogovora',
        name: 'Ime',
        namePlaceholder: 'Ime spremenljivke',
        type: 'Vrsta',
        value: 'Privzeta vrednost',
        valuePlaceholder: 'Privzeta vrednost, pustite prazno, če je ne želite nastaviti',
        description: 'Opis',
        descriptionPlaceholder: 'Opišite spremenljivko',
        editInJSON: 'Uredi v JSON',
        oneByOne: 'Dodaj eno po eno',
        editInForm: 'Uredi v obrazcu',
        arrayValue: 'Vrednost',
        addArrayValue: 'Dodaj vrednost',
        objectKey: 'Ključ',
        objectType: 'Vrsta',
        objectValue: 'Privzeta vrednost',
      },
      storedContent: 'Shranjena vsebina',
      updatedAt: 'Posodobljeno ob',
    },
    changeHistory: {
      title: 'Zgodovina sprememb',
      placeholder: 'Še niste ničesar spremenili',
      clearHistory: 'Počisti zgodovino',
      hint: 'Namig',
      hintText: 'Vaša dejanja urejanja se spremljajo v zgodovini sprememb, ki se hrani na vaši napravi med trajanjem te seje. Ta zgodovina se bo izbrisala, ko zapustite urejevalnik.',
      stepBackward_one: '{{count}} korak nazaj',
      stepBackward_other: '{{count}} korakov nazaj',
      stepForward_one: '{{count}} korak naprej',
      stepForward_other: '{{count}} korakov naprej',
      sessionStart: 'Začetek seje',
      currentState: 'Trenutno stanje',
      nodeTitleChange: 'Naslov bloka spremenjen',
      nodeDescriptionChange: 'Opis bloka spremenjen',
      nodeDragStop: 'Blok premaknjen',
      nodeChange: 'Blok spremenjen',
      nodeConnect: 'Blok povezan',
      nodePaste: 'Blok prilepljen',
      nodeDelete: 'Blok izbrisan',
      nodeAdd: 'Blok dodan',
      nodeResize: 'Velikost bloka spremenjena',
      noteAdd: 'Opomba dodana',
      noteChange: 'Opomba spremenjena',
      noteDelete: 'Opomba izbrisana',
      edgeDelete: 'Blok prekinjen',
    },
    errorMsg: {
      fieldRequired: '{{field}} je obvezen',
      rerankModelRequired: 'Pred vklopom modela za ponovno razvrščanje, prosimo potrdite, da je bil model uspešno konfiguriran v nastavitvah.',
      authRequired: 'Potrebna je avtorizacija',
      invalidJson: '{{field}} je neveljaven JSON',
      fields: {
        variable: 'Ime spremenljivke',
        variableValue: 'Vrednost spremenljivke',
        code: 'Koda',
        model: 'Model',
        rerankModel: 'Model za ponovno razvrščanje',
      },
      invalidVariable: 'Neveljavna spremenljivka',
    },
    singleRun: {
      testRun: 'Testni zagon',
      startRun: 'Začni zagon',
      running: 'V teku',
      testRunIteration: 'Iteracija testnega zagona',
      back: 'Nazaj',
      iteration: 'Iteracija',
    },
    tabs: {
      'searchBlock': 'Iskanje bloka',
      'blocks': 'Bloki',
      'searchTool': 'Iskanje orodja',
      'tools': 'Orodja',
      'allTool': 'Vsa',
      'builtInTool': 'Vgrajena',
      'customTool': 'Prilagojena',
      'workflowTool': 'Potek dela',
      'question-understand': 'Razumevanje vprašanja',
      'logic': 'Logika',
      'transform': 'Pretvorba',
      'utilities': 'Pripomočki',
      'noResult': 'Ni najdenih zadetkov',
    },
    blocks: {
      'start': 'Začetek',
      'end': 'Konec',
      'answer': 'Odgovor',
      'llm': 'LLM',
      'knowledge-retrieval': 'Pridobivanje znanja',
      'question-classifier': 'Klasifikator vprašanj',
      'if-else': 'IF/ELSE',
      'code': 'Koda',
      'template-transform': 'Predloga',
      'http-request': 'HTTP zahteva',
      'variable-assigner': 'Dodeljevalec spremenljivk',
      'variable-aggregator': 'Zbiralnik spremenljivk',
      'assigner': 'Dodeljevalec spremenljivk',
      'iteration-start': 'Začetek iteracije',
      'iteration': 'Iteracija',
      'parameter-extractor': 'Izvleček parametrov',
    },
    blocksAbout: {
      'start': 'Določite začetne parametre za zagon delovnega toka',
      'end': 'Določite konec in vrsto rezultata delovnega toka',
      'answer': 'Določite vsebino odgovora v klepetalni konverzaciji',
      'llm': 'Klicanje velikih jezikovnih modelov za odgovarjanje na vprašanja ali obdelavo naravnega jezika',
      'knowledge-retrieval': 'Omogoča iskanje vsebine, povezane z uporabnikovimi vprašanji, iz baze znanja',
      'question-classifier': 'Določite pogoje za klasifikacijo uporabniških vprašanj; LLM lahko določi, kako se bo konverzacija razvijala glede na klasifikacijski opis',
      'if-else': 'Omogoča razdelitev delovnega toka na dve veji glede na pogoje if/else',
      'code': 'Izvedite del kode Python ali NodeJS za implementacijo prilagojene logike',
      'template-transform': 'Pretvorite podatke v niz z uporabo Jinja predloge',
      'http-request': 'Omogoča pošiljanje strežniških zahtev preko HTTP protokola',
      'variable-assigner': 'Združi spremenljivke več vej v eno spremenljivko za enotno konfiguracijo vozlišč nižje v poteku.',
      'assigner': 'Vozlišče za dodelitev spremenljivk se uporablja za dodelitev vrednosti pisnim spremenljivkam (kot so spremenljivke konverzacije).',
      'variable-aggregator': 'Združi spremenljivke več vej v eno spremenljivko za enotno konfiguracijo vozlišč nižje v poteku.',
      'iteration': 'Izvedite več korakov na seznamu objektov, dokler niso vsi rezultati izpisani.',
      'parameter-extractor': 'Uporabite LLM za izvleček strukturiranih parametrov iz naravnega jezika za klice orodij ali HTTP zahteve.',
    },
    operator: {
      zoomIn: 'Povečaj',
      zoomOut: 'Pomanjšaj',
      zoomTo50: 'Povečaj na 50%',
      zoomTo100: 'Povečaj na 100%',
      zoomToFit: 'Prilagodi velikost',
    },
    panel: {
      userInputField: 'Vnosno polje uporabnika',
      changeBlock: 'Spremeni blok',
      helpLink: 'Povezava za pomoč',
      about: 'O',
      createdBy: 'Ustvaril ',
      nextStep: 'Naslednji korak',
      addNextStep: 'Dodaj naslednji blok v tem delovnem toku',
      selectNextStep: 'Izberi naslednji blok',
      runThisStep: 'Zaženi ta korak',
      checklist: 'Kontrolni seznam',
      checklistTip: 'Poskrbite, da so vsi problemi rešeni pred objavo',
      checklistResolved: 'Vsi problemi so rešeni',
      organizeBlocks: 'Organiziraj bloke',
      change: 'Spremeni',
      optional: '(neobvezno)',
    },
    nodes: {
      common: {
        outputVars: 'Izhodne spremenljivke',
        insertVarTip: 'Vstavi spremenljivko',
        memory: {
          memory: 'Pomnjenje',
          memoryTip: 'Nastavitve pomnjenja klepeta',
          windowSize: 'Velikost okna',
          conversationRoleName: 'Ime vloge v konverzaciji',
          user: 'Predpona uporabnika',
          assistant: 'Predpona pomočnika',
        },
        memories: {
          title: 'Pomnjenje',
          tip: 'Pomnjenje klepeta',
          builtIn: 'Vgrajeno',
        },
      },
      start: {
        required: 'obvezno',
        inputField: 'Vnosno polje',
        builtInVar: 'Vgrajene spremenljivke',
        outputVars: {
          query: 'Uporabniški vnos',
          memories: {
            des: 'Zgodovina konverzacije',
            type: 'vrsta sporočila',
            content: 'vsebina sporočila',
          },
          files: 'Seznam datotek',
        },
        noVarTip: 'Nastavite vnose, ki jih lahko uporabite v delovnem toku',
      },
      end: {
        outputs: 'Izhodi',
        output: {
          type: 'vrsta izhoda',
          variable: 'izhoda spremenljivka',
        },
        type: {
          'none': 'Brez',
          'plain-text': 'Navadno besedilo',
          'structured': 'Strukturirano',
        },
      },
      answer: {
        answer: 'Odgovor',
        outputVars: 'Izhodne spremenljivke',
      },
      llm: {
        model: 'model',
        variables: 'spremenljivke',
        context: 'kontekst',
        contextTooltip: 'Kot kontekst lahko uvozite Znanje',
        notSetContextInPromptTip: 'Za omogočanje funkcije konteksta, prosimo, izpolnite kontekstno spremenljivko v POZIVU.',
        prompt: 'poziv',
        roleDescription: {
          system: 'Podajte splošna navodila za konverzacijo',
          user: 'Podajte navodila, poizvedbe ali katero koli besedilno vsebino za model',
          assistant: 'Odzivi modela na uporabniška sporočila',
        },
        addMessage: 'Dodaj sporočilo',
        vision: 'vizija',
        files: 'Datoteke',
        resolution: {
          name: 'Ločljivost',
          high: 'Visoka',
          low: 'Nizka',
        },
        outputVars: {
          output: 'Generirana vsebina',
          usage: 'Podatki o uporabi modela',
        },
        singleRun: {
          variable: 'Spremenljivka',
        },
        sysQueryInUser: 'sys.query v uporabniškem sporočilu je obvezno',
      },
      knowledgeRetrieval: {
        queryVariable: 'Poizvedbena spremenljivka',
        knowledge: 'Znanje',
        outputVars: {
          output: 'Pridobljeni segmentirani podatki',
          content: 'Segmentirana vsebina',
          title: 'Segmentirani naslov',
          icon: 'Segmentirana ikona',
          url: 'Segmentiran URL',
          metadata: 'Drugi metapodatki',
        },
      },
      http: {
        inputVars: 'Vnosne spremenljivke',
        api: 'API',
        apiPlaceholder: 'Vnesite URL, vstavite spremenljivko z tipko ‘/’',
        notStartWithHttp: 'API mora začeti z http:// ali https://',
        key: 'Ključ',
        value: 'Vrednost',
        bulkEdit: 'Serijsko urejanje',
        keyValueEdit: 'Urejanje ključ-vrednost',
        headers: 'Glave',
        params: 'Parametri',
        body: 'Telo',
        outputVars: {
          body: 'Vsebina odgovora',
          statusCode: 'Statusna koda odgovora',
          headers: 'Seznam glave odgovora v JSON',
          files: 'Seznam datotek',
        },
      },
      authorization: {
        'authorization': 'Avtorizacija',
        'authorizationType': 'Vrsta avtorizacije',
        'no-auth': 'Brez',
        'api-key': 'API-ključ',
        'auth-type': 'Vrsta avtorizacije',
        'basic': 'Osnovna',
        'bearer': 'Imetnik',
        'custom': 'Prilagojena',
        'api-key-title': 'API ključ',
        'header': 'Glava',
      },
      insertVarPlaceholder: 'vnesite \'/\' za vstavljanje spremenljivke',
      timeout: {
        title: 'Časovna omejitev',
        connectLabel: 'Časovna omejitev povezave',
        connectPlaceholder: 'Vnesite časovno omejitev povezave v sekundah',
        readLabel: 'Časovna omejitev branja',
        readPlaceholder: 'Vnesite časovno omejitev branja v sekundah',
        writeLabel: 'Časovna omejitev pisanja',
        writePlaceholder: 'Vnesite časovno omejitev pisanja v sekundah',
      },
    },
    code: {
      inputVars: 'Vhodne spremenljivke',
      outputVars: 'Izhodne spremenljivke',
      advancedDependencies: 'Napredne odvisnosti',
      advancedDependenciesTip: 'Dodajte nekaj prednaloženih odvisnosti, ki potrebujejo več časa za nalaganje ali niso privzeto vgrajene',
      searchDependencies: 'Išči odvisnosti',
    },
    templateTransform: {
      inputVars: 'Vhodne spremenljivke',
      code: 'Koda',
      codeSupportTip: 'Podpira samo Jinja2',
      outputVars: {
        output: 'Pretvorjena vsebina',
      },
    },
    ifElse: {
      if: 'Če',
      else: 'Sicer',
      elseDescription: 'Uporablja se za definiranje logike, ki naj se izvede, ko pogoj "če" ni izpolnjen.',
      and: 'in',
      or: 'ali',
      operator: 'Operater',
      notSetVariable: 'Najprej nastavite spremenljivko',
      comparisonOperator: {
        'contains': 'vsebuje',
        'not contains': 'ne vsebuje',
        'start with': 'se začne z',
        'end with': 'se konča z',
        'is': 'je',
        'is not': 'ni',
        'empty': 'je prazna',
        'not empty': 'ni prazna',
        'null': 'je null',
        'not null': 'ni null',
      },
      enterValue: 'Vnesite vrednost',
      addCondition: 'Dodaj pogoj',
      conditionNotSetup: 'Pogoj NI nastavljen',
      selectVariable: 'Izberite spremenljivko...',
    },
    variableAssigner: {
      title: 'Dodeli spremenljivke',
      outputType: 'Vrsta izhoda',
      varNotSet: 'Spremenljivka ni nastavljena',
      noVarTip: 'Dodajte spremenljivke, ki jih želite dodeliti',
      type: {
        string: 'Niz',
        number: 'Število',
        object: 'Objekt',
        array: 'Polje',
      },
      aggregationGroup: 'Skupina za združevanje',
      aggregationGroupTip: 'Omogočanje te funkcije omogoča agregatorju spremenljivk združevanje več naborov spremenljivk.',
      addGroup: 'Dodaj skupino',
      outputVars: {
        varDescribe: 'Izhod {{groupName}}',
      },
      setAssignVariable: 'Nastavi dodeljeno spremenljivko',
    },
    assigner: {
      'assignedVariable': 'Dodeljena spremenljivka',
      'writeMode': 'Način pisanja',
      'writeModeTip': 'Način dodajanja: Na voljo samo za spremenljivke vrste polje.',
      'over-write': 'Prepiši',
      'append': 'Dodaj',
      'plus': 'Plus',
      'clear': 'Počisti',
      'setVariable': 'Nastavi spremenljivko',
      'variable': 'Spremenljivka',
    },
    tool: {
      toAuthorize: 'Za avtorizacijo',
      inputVars: 'Vhodne spremenljivke',
      outputVars: {
        text: 'orodje je ustvarilo vsebino',
        files: {
          title: 'orodje je ustvarilo datoteke',
          type: 'Podprta vrsta. Trenutno podpira samo slike',
          transfer_method: 'Način prenosa. Vrednosti so remote_url ali local_file',
          url: 'URL slike',
          upload_file_id: 'ID naložene datoteke',
        },
        json: 'orodje je ustvarilo json',
      },
    },
    questionClassifiers: {
      model: 'model',
      inputVars: 'Vhodne spremenljivke',
      outputVars: {
        className: 'Ime razreda',
      },
      class: 'Razred',
      classNamePlaceholder: 'Vnesite ime razreda',
      advancedSetting: 'Napredna nastavitev',
      topicName: 'Ime teme',
      topicPlaceholder: 'Vnesite ime teme',
      addClass: 'Dodaj razred',
      instruction: 'Navodilo',
      instructionTip: 'Vnesite dodatna navodila, da bo klasifikator vprašanj lažje razumel, kako kategorizirati vprašanja.',
      instructionPlaceholder: 'Vnesite vaše navodilo',
    },
    parameterExtractor: {
      inputVar: 'Vhodna spremenljivka',
      extractParameters: 'Izvleči parametre',
      importFromTool: 'Uvozi iz orodij',
      addExtractParameter: 'Dodaj izvlečen parameter',
      addExtractParameterContent: {
        name: 'Ime',
        namePlaceholder: 'Ime izvlečenega parametra',
        type: 'Vrsta',
        typePlaceholder: 'Vrsta izvlečenega parametra',
        description: 'Opis',
        descriptionPlaceholder: 'Opis izvlečenega parametra',
        required: 'Obvezno',
        requiredContent: 'Obvezno je uporabljeno samo kot referenca za sklepanja modela in ne za obvezno preverjanje izhoda parametra.',
      },
      extractParametersNotSet: 'Parametri za izvleček niso nastavljeni',
      instruction: 'Navodilo',
      instructionTip: 'Vnesite dodatna navodila, da parameter extractor lažje razume, kako izvleči parametre.',
      advancedSetting: 'Napredna nastavitev',
      reasoningMode: 'Način sklepanja',
      reasoningModeTip: 'Lahko izberete ustrezen način sklepanja glede na sposobnost modela za odgovore na navodila za klice funkcij ali pozive.',
      isSuccess: 'Je uspeh. Pri uspehu je vrednost 1, pri neuspehu pa 0.',
      errorReason: 'Razlog za napako',
    },
    iteration: {
      deleteTitle: 'Izbrisati vozlišče iteracije?',
      deleteDesc: 'Brisanje vozlišča iteracije bo izbrisalo vsa podrejena vozlišča',
      input: 'Vhod',
      output: 'Izhodne spremenljivke',
      iteration_one: '{{count}} iteracija',
      iteration_other: '{{count}} iteracij',
      currentIteration: 'Trenutna iteracija',
    },
    note: {
      addNote: 'Dodaj opombo',
      editor: {
        placeholder: 'Zapišite opombo...',
        small: 'Majhno',
        medium: 'Srednje',
        large: 'Veliko',
        bold: 'Krepko',
        italic: 'Poševno',
        strikethrough: 'Prečrtano',
        link: 'Povezava',
        openLink: 'Odpri',
        unlink: 'Odstrani povezavo',
        enterUrl: 'Vnesite URL...',
        invalidUrl: 'Neveljaven URL',
        bulletList: 'Označen seznam',
        showAuthor: 'Pokaži avtorja',
      },
    },
  },
  tracing: {
    stopBy: 'Ustavljeno s strani {{user}}',
  },
  chatVariable: {
    modal: {
      type: 'Vrsta',
      objectValue: 'Privzeta vrednost',
      description: 'Opis',
      editTitle: 'Urejanje spremenljivke pogovora',
      namePlaceholder: 'Ime spremenljivke',
      valuePlaceholder: 'Privzeta vrednost, pustite prazno, da ni nastavljeno',
      title: 'Dodajanje spremenljivke pogovora',
      editInJSON: 'Urejanje v JSON',
      value: 'Privzeta vrednost',
      oneByOne: 'Dodajanje enega za drugim',
      objectKey: 'Ključ',
      objectType: 'Vrsta',
      arrayValue: 'Vrednost',
      name: 'Ime',
      descriptionPlaceholder: 'Opis spremenljivke',
      editInForm: 'Uredi v obrazcu',
      addArrayValue: 'Dodajanje vrednosti',
    },
    storedContent: 'Shranjena vsebina',
    updatedAt: 'Posodobljeno na',
    panelTitle: 'Spremenljivke pogovora',
    button: 'Dodajanje spremenljivke',
    panelDescription: 'Spremenljivke pogovora se uporabljajo za shranjevanje interaktivnih informacij, ki si jih mora LLM zapomniti, vključno z zgodovino pogovorov, naloženimi datotekami, uporabniškimi nastavitvami. So branje in pisanje.',
    docLink: 'Če želite izvedeti več, obiščite naše dokumente.',
  },
  changeHistory: {
    nodeChange: 'Blokiranje spremenjeno',
    placeholder: 'Ničesar še niste spremenili',
    nodeDescriptionChange: 'Opis bloka je bil spremenjen',
    nodePaste: 'Blokiranje lepljenja',
    noteDelete: 'Opomba izbrisana',
    nodeDragStop: 'Blok premaknjen',
    nodeConnect: 'Blok povezan',
    sessionStart: 'Začetek seje',
    nodeDelete: 'Blokiraj izbrisane',
    stepBackward_other: '{{count}} stopi nazaj',
    hint: 'Namig',
    noteAdd: 'Opomba dodana',
    clearHistory: 'Počisti zgodovino',
    stepForward_one: '{{count}} korak naprej',
    stepBackward_one: '{{count}} korak nazaj',
    nodeAdd: 'Blokiranje dodano',
    noteChange: 'Opomba spremenjena',
    hintText: 'Dejanjem urejanja se sledi v zgodovini sprememb, ki je shranjena v napravi za čas trajanja te seje. Ta zgodovina bo izbrisana, ko zapustite urejevalnik.',
    stepForward_other: '{{count}} koraki naprej',
    edgeDelete: 'Blok je prekinjen.',
    nodeTitleChange: 'Naslov bloka spremenjen',
    nodeResize: 'Spremeni velikost bloka',
    title: 'Zgodovina sprememb',
    currentState: 'Trenutno stanje',
  },
  errorMsg: {
    fields: {
      code: 'Koda',
      variableValue: 'Vrednost spremenljivke',
      visionVariable: 'Spremenljivka vida',
      model: 'Model',
      rerankModel: 'Ponovno razvrsti model',
      variable: 'Ime spremenljivke',
    },
    invalidJson: '{{field}} je neveljaven JSON',
    invalidVariable: 'Neveljavna spremenljivka',
    authRequired: 'Dovoljenje je potrebno',
    fieldRequired: '{{field}} je obvezno',
    rerankModelRequired: 'Preden vklopite Rerank Model, preverite, ali je bil model uspešno konfiguriran v nastavitvah.',
    toolParameterRequired: '{{field}}: parameter [{{param}}] je obvezen',
    noValidTool: '{{field}} Izbrano ni veljavno orodje',
  },
  singleRun: {
    startRun: 'Začni zagnati',
    running: 'Tek',
    testRunIteration: 'Ponovitev preskusnega zagona',
    iteration: 'Ponovitev',
    back: 'Hrbet',
    testRun: 'Preskusni zagon',
    loop: 'Zanka',
  },
  tabs: {
    'blocks': 'Bloki',
    'workflowTool': 'Potek dela',
    'transform': 'Preoblikovanje',
    'question-understand': 'Vprašanje razumeti',
    'builtInTool': 'Vgrajeno',
    'allTool': 'Ves',
    'tools': 'Orodja',
    'logic': 'Logika',
    'searchBlock': 'Iskalni blok',
    'noResult': 'Ni najdenega ujemanja',
    'customTool': 'Običaj',
    'utilities': 'Utilities',
    'searchTool': 'Orodje za iskanje',
    'agent': 'Strategija agenta',
    'plugin': 'Vtičnik',
  },
  blocks: {
    'variable-aggregator': 'Spremenljivi agregator',
    'code': 'Koda',
    'parameter-extractor': 'Ekstraktor parametrov',
    'llm': 'LLM',
    'knowledge-retrieval': 'Pridobivanje znanja',
    'answer': 'Odgovoriti',
    'end': 'Konec',
    'document-extractor': 'Ekstraktor dokumentov',
    'assigner': 'Dodeljevalnik spremenljivke',
    'iteration-start': 'Začetek ponovitve',
    'template-transform': 'Predloga',
    'iteration': 'Ponovitev',
    'start': 'Začetek',
    'if-else': 'IF/ELSE',
    'list-operator': 'Operater seznama',
    'http-request': 'Zahteva HTTP',
    'variable-assigner': 'Spremenljivi agregator',
    'question-classifier': 'Klasifikator vprašanj',
    'agent': 'Agent',
    'loop-end': 'Izhod iz zanke',
    'loop': 'Zanka',
    'loop-start': 'Začetek zanke',
  },
  blocksAbout: {
    'document-extractor': 'Uporablja se za razčlenjevanje naloženih dokumentov v besedilno vsebino, ki je zlahka razumljiva LLM.',
    'list-operator': 'Uporablja se za filtriranje ali razvrščanje vsebine matrike.',
    'template-transform': 'Pretvorite podatke v niz s sintakso predloge Jinja',
    'question-classifier': 'Določite pogoje razvrščanja uporabniških vprašanj, LLM lahko določi, kako poteka pogovor na podlagi opisa klasifikacije',
    'start': 'Določanje začetnih parametrov za zagon poteka dela',
    'if-else': 'Omogoča razdelitev poteka dela na dve veji glede na pogoje if/else',
    'knowledge-retrieval': 'Omogoča poizvedovanje po besedilni vsebini, ki je povezana z uporabniškimi vprašanji iz zbirke znanja',
    'variable-assigner': 'Združite spremenljivke z več vejami v eno spremenljivko za poenoteno konfiguracijo nadaljnjih vozlišč.',
    'code': 'Izvedite kodo Python ali NodeJS za izvajanje logike po meri',
    'answer': 'Določanje vsebine odgovora v pogovoru v klepetu',
    'iteration': 'Izvedite več korakov na predmetu seznama, dokler niso prikazani vsi rezultati.',
    'http-request': 'Dovoli pošiljanje zahtev strežnika prek protokola HTTP',
    'end': 'Določanje končne in končne vrste poteka dela',
    'variable-aggregator': 'Združite spremenljivke z več vejami v eno spremenljivko za poenoteno konfiguracijo nadaljnjih vozlišč.',
    'parameter-extractor': 'Uporabite LLM za pridobivanje strukturiranih parametrov iz naravnega jezika za klicanje orodij ali zahteve HTTP.',
    'assigner': 'Vozlišče za dodeljevanje spremenljivk se uporablja za dodeljevanje vrednosti zapisljivim spremenljivkam (kot so spremenljivke pogovora).',
    'llm': 'Sklicevanje na velike jezikovne modele za odgovarjanje na vprašanja ali obdelavo naravnega jezika',
    'agent': 'Sklicevanje na velike jezikovne modele za odgovarjanje na vprašanja ali obdelavo naravnega jezika',
    'loop': 'Izvedite zanko logike, dokler ni izpolnjen pogoj za prekinitev ali dokler ni dosežena največja število ponovitev.',
    'loop-end': 'Enakovredno „prekini“. Ta vozlišče nima konfiguracijskih elementov. Ko telo zanke doseže to vozlišče, zanka preneha.',
  },
  operator: {
    zoomOut: 'Pomanjšanje',
    zoomTo100: 'Povečava na 100 %',
    zoomToFit: 'Povečaj, da se prilega',
    zoomIn: 'Povečava',
    zoomTo50: 'Povečava na 50%',
  },
  panel: {
    helpLink: 'Povezava za pomoč',
    organizeBlocks: 'Organiziranje blokov',
    optional: '(neobvezno)',
    nextStep: 'Naslednji korak',
    checklist: 'Kontrolni seznam',
    runThisStep: 'Zaženite ta korak',
    about: 'Približno',
    selectNextStep: 'Izberite Naslednji blok',
    changeBlock: 'Spremeni blok',
    createdBy: 'Ustvaril',
    checklistTip: 'Pred objavo se prepričajte, da so vse težave odpravljene',
    userInputField: 'Uporabniško polje za vnos',
    checklistResolved: 'Vse težave so odpravljene',
    addNextStep: 'Dodajanje naslednjega bloka v ta potek dela',
    change: 'Spremeniti',
    moveToThisNode: 'Premakni se na to vozlišče',
  },
  nodes: {
    common: {
      memory: {
        conversationRoleName: 'Ime vloge pogovora',
        memoryTip: 'Nastavitve pomnilnika klepeta',
        assistant: 'Predpona pomočnika',
        user: 'Uporabniška predpona',
        memory: 'Spomin',
        windowSize: 'Velikost okna',
      },
      memories: {
        tip: 'Pomnilnik klepeta',
        title: 'Spomine',
        builtIn: 'Vgrajeno',
      },
      outputVars: 'Izhodne spremenljivke',
      insertVarTip: 'Vstavi spremenljivko',
      errorHandle: {
        none: {
          desc: 'Vozlišče se bo prenehalo izvajati, če pride do izjeme in ne bo obravnavano',
          title: 'Nobena',
        },
        defaultValue: {
          output: 'Izhodna privzeta vrednost',
          tip: 'Po napaki se vrne pod vrednost.',
          title: 'Privzeta vrednost',
          inLog: 'Izjema vozlišča, izhod v skladu s privzetimi vrednostmi.',
          desc: 'Ko pride do napake, določite statično izhodno vsebino.',
        },
        failBranch: {
          title: 'Neuspešna veja',
          inLog: 'Izjema vozlišča, bo samodejno izvedla neuspešno vejo. Izhod vozlišča bo vrnil vrsto napake in sporočilo o napaki ter ju posredoval navzdol.',
          desc: 'Ko pride do napake, bo izvedla vejo izjeme',
          customizeTip: 'Ko je aktivirana veja neuspeha, izjeme, ki jih vržejo vozlišča, ne bodo prekinile procesa. Namesto tega bo samodejno izvedel vnaprej določeno vejo neuspeha, kar vam bo omogočilo prilagodljivo zagotavljanje sporočil o napakah, poročil, popravkov ali preskakovanja dejanj.',
          customize: 'Pojdite na platno, da prilagodite logiko neuspešne veje.',
        },
        partialSucceeded: {
          tip: 'V procesu so {{num}} vozlišča, ki se izvajajo nenormalno, pojdite na sledenje, da preverite dnevnike.',
        },
        title: 'Ravnanje z napakami',
        tip: 'Strategija ravnanja z izjemami, ki se sproži, ko vozlišče naleti na izjemo.',
      },
      retry: {
        retryOnFailure: 'Ponovni poskus ob neuspehu',
        retryInterval: 'Interval ponovnega poskusa',
        retrying: 'Ponovnim...',
        retry: 'Ponoviti',
        retryFailedTimes: '{{times}} ponovni poskusi niso uspeli',
        retries: '{{num}} Poskusov',
        times: 'Krat',
        retryTimes: 'Ponovni poskus {{times}}-krat ob neuspehu',
        retryFailed: 'Ponovni poskus ni uspel',
        retrySuccessful: 'Ponovni poskus je bil uspešen',
        maxRetries: 'Največ ponovnih poskusov',
        ms: 'Ms',
      },
    },
    start: {
      outputVars: {
        memories: {
          content: 'Vsebina sporočila',
          des: 'Zgodovina pogovorov',
          type: 'Vrsta sporočila',
        },
        query: 'Uporabniški vnos',
        files: 'Seznam datotek',
      },
      required: 'Zahteva',
      inputField: 'Vnosno polje',
      noVarTip: 'Nastavitev vhodov, ki jih je mogoče uporabiti v poteku dela',
      builtInVar: 'Vgrajene spremenljivke',
    },
    end: {
      output: {
        variable: 'izhodna spremenljivka',
        type: 'Vrsta izhoda',
      },
      type: {
        'structured': 'Strukturiran',
        'plain-text': 'Navadno besedilo',
        'none': 'Nobena',
      },
      outputs: 'Izhodov',
    },
    answer: {
      answer: 'Odgovoriti',
      outputVars: 'Izhodne spremenljivke',
    },
    llm: {
      roleDescription: {
        assistant: 'Odgovori modela na podlagi sporočil uporabnikov',
        system: 'Podajte navodila na visoki ravni za pogovor',
        user: 'Navedite navodila, poizvedbe ali kakršen koli besedilni vnos v model',
      },
      resolution: {
        low: 'Nizek',
        high: 'Visok',
        name: 'Resolucija',
      },
      outputVars: {
        usage: 'Informacije o uporabi modela',
        output: 'Ustvarjanje vsebine',
      },
      singleRun: {
        variable: 'Spremenljivka',
      },
      notSetContextInPromptTip: 'Če želite omogočiti funkcijo konteksta, izpolnite kontekstno spremenljivko v PROMPT.',
      sysQueryInUser: 'sys.query v sporočilu uporabnika je obvezen',
      model: 'model',
      files: 'Datoteke',
      addMessage: 'Dodaj sporočilo',
      context: 'Kontekstu',
      variables: 'Spremenljivke',
      prompt: 'Uren',
      vision: 'vid',
      contextTooltip: 'Znanje lahko uvozite kot kontekst',
      jsonSchema: {
        warningTips: {
          saveSchema: 'Prosimo, da dokončate urejanje trenutnega polja, preden shranite shemo.',
        },
        addChildField: 'Dodaj polje za otroka',
        instruction: 'Navodilo',
        regenerate: 'Ponovno generiranje',
        back: 'Nazaj',
        generationTip: 'Lahko uporabite naravni jezik za hitro ustvarjanje JSON sheme.',
        title: 'Strukturirana izhodna shema',
        generating: 'Generiranje JSON sheme...',
        showAdvancedOptions: 'Prikaži napredne možnosti',
        promptTooltip: 'Pretvorite besedilni opis v standardizirano strukturo JSON sheme.',
        generateJsonSchema: 'Generiraj JSON shemo',
        fieldNamePlaceholder: 'Ime polja',
        apply: 'Prijavi se',
        doc: 'Izvedite več o strukturiranem izhodu',
        promptPlaceholder: 'Opiši svoj JSON shemo...',
        generatedResult: 'Generiran rezultat',
        import: 'Uvoz iz JSON',
        generate: 'Generirati',
        resultTip: 'Tukaj je generiran rezultat. Če niste zadovoljni, se lahko vrnete in spremenite svoj poziv.',
        stringValidations: 'Preverjanje nizov',
        descriptionPlaceholder: 'Dodajte opis',
        required: 'zahtevano',
        addField: 'Dodaj polje',
        resetDefaults: 'Ponastavi',
      },
    },
    knowledgeRetrieval: {
      outputVars: {
        title: 'Segmentirani naslov',
        url: 'Segmentirani URL',
        output: 'Pridobivanje segmentiranih podatkov',
        icon: 'Segmentirana ikona',
        metadata: 'Drugi metapodatki',
        content: 'Segmentirana vsebina',
      },
      queryVariable: 'Spremenljivka poizvedbe',
      knowledge: 'Znanje',
      metadata: {
        options: {
          disabled: {
            title: 'Onemogočeno',
            subTitle: 'Ne omogočanje filtriranja metapodatkov',
          },
          automatic: {
            desc: 'Samodejno ustvarite filtrirne pogoje za metapodatke na podlagi spremenljivke poizvedbe',
            subTitle: 'Samodejno ustvarite filtrirne pogoje za metapodatke na podlagi uporabniškega poizvedovanja.',
            title: 'Samodejno',
          },
          manual: {
            title: 'Ročno',
            subTitle: 'Ročno dodajte pogoje za filtriranje metapodatkov',
          },
        },
        panel: {
          title: 'Pogoji za filtriranje metapodatkov',
          search: 'Išči metapodatke',
          placeholder: 'Vnesite vrednost',
          select: 'Izberi spremenljivko...',
          conditions: 'Pogoji',
          datePlaceholder: 'Izberi čas...',
          add: 'Dodaj pogoj',
        },
        title: 'Filtriranje metapodatkov',
      },
    },
    http: {
      outputVars: {
        headers: 'JSON seznama glav odgovorov',
        body: 'Vsebina odgovora',
        files: 'Seznam datotek',
        statusCode: 'Koda stanja odgovora',
      },
      authorization: {
        'authorization': 'Dovoljenje',
        'header': 'Glava',
        'bearer': 'Nosilec',
        'api-key-title': 'API ključ',
        'basic': 'Osnoven',
        'no-auth': 'Nobena',
        'custom': 'Običaj',
        'authorizationType': 'Vrsta dovoljenja',
        'auth-type': 'Vrsta preverjanja pristnosti',
        'api-key': 'Ključ API-ja',
      },
      timeout: {
        readPlaceholder: 'Vnos časovne omejitve branja v sekundah',
        writePlaceholder: 'Vnesite časovno omejitev pisanja v sekundah',
        writeLabel: 'Časovna omejitev pisanja',
        connectLabel: 'Časovna omejitev povezave',
        title: 'Timeout',
        readLabel: 'Časovna omejitev branja',
        connectPlaceholder: 'Vnos časovne omejitve povezave v sekundah',
      },
      value: 'Vrednost',
      key: 'Ključ',
      notStartWithHttp: 'API se mora začeti z http:// ali https://',
      body: 'Telo',
      type: 'Vrsta',
      inputVars: 'Vhodne spremenljivke',
      bulkEdit: 'Urejanje v velikem obsegu',
      insertVarPlaceholder: 'vnesite "/" za vstavljanje spremenljivke',
      api: 'API',
      keyValueEdit: 'Urejanje ključ-vrednost',
      binaryFileVariable: 'Spremenljivka binarne datoteke',
      headers: 'Glave',
      apiPlaceholder: 'Vnesite URL, vnesite \'/\' vstavi spremenljivko',
      extractListPlaceholder: 'Vnesite indeks elementa seznama, vnesite \'/\' vstavi spremenljivko',
      params: 'Params',
      curl: {
        title: 'Uvoz iz cURL',
        placeholder: 'Tukaj prilepite niz cURL',
      },
    },
    code: {
      inputVars: 'Vhodne spremenljivke',
      outputVars: 'Izhodne spremenljivke',
      searchDependencies: 'Odvisnosti iskanja',
      advancedDependenciesTip: 'Tukaj dodajte nekaj vnaprej naloženih odvisnosti, ki trajajo dlje časa ali niso privzeto vgrajene',
      advancedDependencies: 'Napredne odvisnosti',
    },
    templateTransform: {
      outputVars: {
        output: 'Preoblikovana vsebina',
      },
      code: 'Koda',
      inputVars: 'Vhodne spremenljivke',
      codeSupportTip: 'Podpira samo Jinja2',
    },
    ifElse: {
      comparisonOperator: {
        'all of': 'vse',
        'is not': 'ni',
        'not empty': 'ni prazen',
        'start with': 'Začnite z',
        'is': 'Je',
        'null': 'je nična',
        'not exists': 'ne obstaja',
        'contains': 'Vsebuje',
        'empty': 'je prazen',
        'exists': 'Obstaja',
        'in': 'v',
        'not contains': 'ne vsebuje',
        'end with': 'Končaj z',
        'not in': 'ni v',
        'not null': 'ni nična',
        'after': 'po',
        'before': 'pred',
      },
      optionName: {
        video: 'Video',
        doc: 'Doc',
        audio: 'Avdio',
        image: 'Podoba',
        url: 'Spletni naslov',
        localUpload: 'Lokalno nalaganje',
      },
      and: 'in',
      else: 'Drugega',
      enterValue: 'Vnesite vrednost',
      elseDescription: 'Uporablja se za določanje logike, ki jo je treba izvesti, ko pogoj if ni izpolnjen.',
      addCondition: 'Dodajanje pogoja',
      if: 'Če',
      select: 'Izbrati',
      selectVariable: 'Izberite spremenljivko ...',
      conditionNotSetup: 'Pogoj NI nastavljen',
      addSubVariable: 'Podspremenljivka',
      notSetVariable: 'Prosimo, najprej nastavite spremenljivko',
      operator: 'Operaterja',
      or: 'ali',
      condition: 'Pogoji',
    },
    variableAssigner: {
      type: {
        string: 'Niz',
        object: 'Predmet',
        array: 'Matrika',
        number: 'Številka',
      },
      outputVars: {
        varDescribe: '{{groupName}} izhod',
      },
      addGroup: 'Dodajanje skupine',
      outputType: 'Vrsta izhoda',
      title: 'Dodeljevanje spremenljivk',
      noVarTip: 'Seštevanje spremenljivk, ki jih je treba dodeliti',
      aggregationGroupTip: 'Če omogočite to funkcijo, lahko združevalnik spremenljivk združi več naborov spremenljivk.',
      aggregationGroup: 'Združevalna skupina',
      varNotSet: 'Spremenljivka ni nastavljena',
      setAssignVariable: 'Nastavitev spremenljivke dodelitve',
    },
    assigner: {
      'writeMode': 'Način pisanja',
      'plus': 'Plus',
      'variable': 'Spremenljivka',
      'clear': 'Jasen',
      'append': 'Dodaj',
      'assignedVariable': 'Dodeljena spremenljivka',
      'setVariable': 'Nastavi spremenljivko',
      'over-write': 'Prepisati',
      'writeModeTip': 'Način dodajanja: Na voljo samo za spremenljivke polja.',
      'operations': {
        '+=': '+=',
        'overwrite': 'Prepisati',
        '*=': '*=',
        'extend': 'Razširiti',
        'append': 'Dodaj',
        '-=': '-=',
        'title': 'Operacija',
        '/=': '/=',
        'set': 'Nastaviti',
        'clear': 'Jasen',
        'over-write': 'Prepisati',
        'remove-last': 'Odstrani zadnje',
        'remove-first': 'Odstrani prvi',
      },
      'variables': 'Spremenljivke',
      'selectAssignedVariable': 'Izberite dodeljeno spremenljivko ...',
      'assignedVarsDescription': 'Dodeljene spremenljivke morajo biti zapisljive, kot so spremenljivke pogovora.',
      'noVarTip': 'Kliknite gumb »+«, da dodate spremenljivke',
      'noAssignedVars': 'Ni razpoložljivih dodeljenih spremenljivk',
      'varNotSet': 'Spremenljivka NI nastavljena',
      'setParameter': 'Nastavi parameter ...',
    },
    tool: {
      outputVars: {
        files: {
          transfer_method: 'Način prenosa. Vrednost je remote_url ali local_file',
          upload_file_id: 'Naloži ID datoteke',
          type: 'Vrsta podpore. Zdaj podpiramo samo sliko',
          url: 'URL slike',
          title: 'Datoteke, ustvarjene z orodjem',
        },
        json: 'JSON, ustvarjen z orodjem',
        text: 'Vsebina, ustvarjena z orodjem',
      },
      inputVars: 'Vhodne spremenljivke',
      toAuthorize: 'Za odobritev',
    },
    questionClassifiers: {
      outputVars: {
        className: 'Ime razreda',
      },
      instruction: 'Navodilo',
      classNamePlaceholder: 'Napišite ime svojega razreda',
      addClass: 'Dodajanje razreda',
      instructionPlaceholder: 'Napišite navodila',
      topicName: 'Ime teme',
      topicPlaceholder: 'Napišite ime teme',
      class: 'Razred',
      advancedSetting: 'Napredne nastavitve',
      model: 'model',
      inputVars: 'Vhodne spremenljivke',
      instructionTip: 'Vnesite dodatna navodila, ki bodo klasifikatorju vprašanj pomagala bolje razumeti, kako kategorizirati vprašanja.',
    },
    parameterExtractor: {
      addExtractParameterContent: {
        description: 'Opis',
        typePlaceholder: 'Vrsta parametra izvlečka',
        requiredContent: 'Zahtevano se uporablja samo kot referenca za sklepanje modela in ne za obvezno validacijo izhodnega parametra.',
        required: 'Zahteva',
        type: 'Vrsta',
        namePlaceholder: 'Izvleček imena parametra',
        descriptionPlaceholder: 'Opis parametra izvlečka',
        name: 'Ime',
      },
      isSuccess: 'Je uspeh.Pri uspehu je vrednost 1, pri neuspehu je vrednost 0.',
      addExtractParameter: 'Dodajanje parametra izvlečka',
      importFromTool: 'Uvoz iz orodij',
      reasoningModeTip: 'Izberete lahko ustrezen način sklepanja glede na sposobnost modela, da se odzove na navodila za klicanje funkcij ali pozive.',
      inputVar: 'Vhodna spremenljivka',
      advancedSetting: 'Napredne nastavitve',
      errorReason: 'Razlog za napako',
      reasoningMode: 'Način sklepanja',
      instruction: 'Navodilo',
      instructionTip: 'Vnesite dodatna navodila, ki bodo ekstraktorju parametrov pomagala razumeti, kako izvleči parametre.',
      extractParametersNotSet: 'Izvleček parametrov ni nastavljen',
      extractParameters: 'Izvleček parametrov',
    },
    iteration: {
      ErrorMethod: {
        continueOnError: 'Nadaljuj ob napaki',
        removeAbnormalOutput: 'Odstranite nenormalen izhod',
        operationTerminated: 'Prekinjena',
      },
      output: 'Izhodne spremenljivke',
      parallelMode: 'Vzporedni način',
      MaxParallelismTitle: 'Največji vzporednost',
      errorResponseMethod: 'Način odziva na napako',
      parallelModeEnableDesc: 'V vzporednem načinu opravila v iteracijah podpirajo vzporedno izvajanje. To lahko konfigurirate na plošči z lastnostmi na desni.',
      error_one: '{{štetje}} Napaka',
      comma: ',',
      parallelModeUpper: 'VZPOREDNI NAČIN',
      parallelModeEnableTitle: 'Vzporedni način omogočen',
      currentIteration: 'Trenutna ponovitev',
      error_other: '{{štetje}} Napake',
      input: 'Vhodni',
      deleteTitle: 'Izbrisati iteracijsko vozlišče?',
      parallelPanelDesc: 'V vzporednem načinu opravila v iteraciji podpirajo vzporedno izvajanje.',
      deleteDesc: 'Če izbrišete iteracijsko vozlišče, boste izbrisali vsa podrejena vozlišča',
      iteration_other: '{{štetje}} Ponovitev',
      answerNodeWarningDesc: 'Opozorilo vzporednega načina: Vozlišča za odgovore, dodelitve spremenljivk pogovora in trajne operacije branja / pisanja v iteracijah lahko povzročijo izjeme.',
      MaxParallelismDesc: 'Največja vzporednost se uporablja za nadzor števila nalog, ki se izvajajo hkrati v eni ponovitvi.',
      iteration_one: '{{štetje}} Ponovitev',
    },
    note: {
      editor: {
        medium: 'Srednja',
        openLink: 'Odprt',
        showAuthor: 'Pokaži avtorja',
        bold: 'Smel',
        strikethrough: 'Prečrtano',
        large: 'Velik',
        link: 'Povezava',
        enterUrl: 'Vnesite URL ...',
        small: 'Majhen',
        italic: 'Ležeče',
        invalidUrl: 'Neveljaven URL',
        unlink: 'Prekini povezavo',
        placeholder: 'Napišite svojo opombo ...',
        bulletList: 'Seznam oznak',
      },
      addNote: 'Dodaj opombo',
    },
    docExtractor: {
      outputVars: {
        text: 'Izvlečeno besedilo',
      },
      inputVar: 'Vhodna spremenljivka',
      learnMore: 'Izvedi več',
      supportFileTypes: 'Podporne vrste datotek: {{types}}.',
    },
    listFilter: {
      outputVars: {
        result: 'Rezultat filtriranja',
        first_record: 'Prvi zapis',
        last_record: 'Zadnji zapis',
      },
      extractsCondition: 'Ekstrahiranje elementa N',
      selectVariableKeyPlaceholder: 'Izberite ključ podspremenljivke',
      asc: 'ASC',
      orderBy: 'Naročite po',
      filterCondition: 'Pogoj filtra',
      filterConditionKey: 'Ključ pogoja filtra',
      desc: 'DESC',
      limit: 'Vrh N',
      filterConditionComparisonOperator: 'Operator za primerjavo pogojev filtra',
      inputVar: 'Vhodna spremenljivka',
      filterConditionComparisonValue: 'Vrednost pogoja filtra',
    },
    agent: {
      strategy: {
        configureTipDesc: 'Po konfiguraciji agentske strategije bo to vozlišče samodejno naložilo preostale konfiguracije. Strategija bo vplivala na mehanizem sklepanja z orodji v več korakih.',
        tooltip: 'Različne agentske strategije določajo, kako sistem načrtuje in izvaja klice orodij v več korakih',
        shortLabel: 'Strategija',
        configureTip: 'Prosimo, konfigurirajte agentsko strategijo.',
        searchPlaceholder: 'Strategija iskalnega agenta',
        label: 'Agentska strategija',
        selectTip: 'Izberite agentsko strategijo',
      },
      pluginInstaller: {
        installing: 'Namestitev',
        install: 'Namestiti',
      },
      modelNotInMarketplace: {
        desc: 'Ta model je nameščen iz lokalnega skladišča ali skladišča GitHub. Uporabite po namestitvi.',
        title: 'Model ni nameščen',
        manageInPlugins: 'Upravljanje v vtičnikih',
      },
      modelNotSupport: {
        descForVersionSwitch: 'Nameščena različica vtičnika ne zagotavlja tega modela. Kliknite, če želite preklopiti med različico.',
        title: 'Nepodprt model',
        desc: 'Nameščena različica vtičnika ne zagotavlja tega modela.',
      },
      modelSelectorTooltips: {
        deprecated: 'Ta model je zastarel',
      },
      outputVars: {
        files: {
          url: 'URL slike',
          title: 'Datoteke, ki jih ustvari agent',
          type: 'Vrsta podpore. Zdaj podpiramo samo sliko',
          upload_file_id: 'Naloži ID datoteke',
          transfer_method: 'Način prenosa. Vrednost je remote_url ali local_file',
        },
        json: 'JSON, ustvarjen z agentom',
        text: 'Vsebina, ki jo ustvari agent',
      },
      checkList: {
        strategyNotSelected: 'Strategija ni izbrana',
      },
      installPlugin: {
        cancel: 'Odpovedati',
        changelog: 'Dnevnik sprememb',
        install: 'Namestiti',
        title: 'Namesti vtičnik',
        desc: 'O namestitvi naslednjega vtičnika',
      },
      strategyNotSet: 'Agentska strategija ni določena',
      modelNotSelected: 'Model ni izbran',
      pluginNotInstalled: 'Ta vtičnik ni nameščen',
      toolNotAuthorizedTooltip: '{{orodje}} Ni pooblaščeno',
      toolbox: 'Orodjarni',
      tools: 'Orodja',
      toolNotInstallTooltip: '{{tool}} ni nameščen',
      strategyNotInstallTooltip: '{{strategy}} ni nameščen',
      modelNotInstallTooltip: 'Ta model ni nameščen',
      pluginNotFoundDesc: 'Ta vtičnik je nameščen iz GitHuba. Prosimo, pojdite na Vtičniki za ponovno namestitev',
      maxIterations: 'Največje število ponovitev',
      notAuthorized: 'Ni pooblaščeno',
      model: 'model',
      learnMore: 'Izvedi več',
      unsupportedStrategy: 'Nepodprta strategija',
      strategyNotFoundDescAndSwitchVersion: 'Nameščena različica vtičnika ne zagotavlja te strategije. Kliknite, če želite preklopiti med različico.',
      strategyNotFoundDesc: 'Nameščena različica vtičnika ne zagotavlja te strategije.',
      configureModel: 'Konfiguracija modela',
      pluginNotInstalledDesc: 'Ta vtičnik je nameščen iz GitHuba. Prosimo, pojdite na Vtičniki za ponovno namestitev',
      linkToPlugin: 'Povezava do vtičnikov',
    },
    loop: {
      ErrorMethod: {
        operationTerminated: 'Prekinjeno',
        continueOnError: 'Nadaljuj ob napaki',
        removeAbnormalOutput: 'Odstrani nenavadne izhode',
      },
      input: 'Vnos',
      inputMode: 'Vnosni način',
      errorResponseMethod: 'Metoda odziva napake',
      setLoopVariables: 'Nastavite spremenljivke znotraj obsega zanke',
      output: 'Izhodna spremenljivka',
      loop_one: '{{count}} Zanka',
      exitConditionTip: 'Vozić potrebuje vsaj eno izhodno pogoj.',
      loopMaxCount: 'Maksimalno število zank',
      deleteDesc: 'Izbris vozlišča zanke bo odstranil vse otroške vozlišča.',
      comma: ',',
      loop_other: '{{count}} Zavoji',
      currentLoop: 'Trenutni obrat',
      variableName: 'Spremenljivka Ime',
      deleteTitle: 'Izbriši vozlišče zanke?',
      error_one: '{{count}} Napaka',
      totalLoopCount: 'Skupno število zank: {{count}}',
      initialLoopVariables: 'Začetne spremenljivke zanke',
      currentLoopCount: 'Trenutno število zank: {{count}}',
      loopNode: 'Ciklični vozlišče',
      loopVariables: 'Zanke Spremenljivke',
      breakConditionTip: 'Lahko se sklicujete le na spremenljivke znotraj zank z zaključnimi pogoji in pogovorne spremenljivke.',
      breakCondition: 'Pogoji za prekinitev zanke',
      finalLoopVariables: 'Končne zanke spremenljivke',
      error_other: '{{count}} Napak',
      loopMaxCountError: 'Prosimo, vnesite veljavno največje število ponovitev, ki mora biti med 1 in {{maxCount}}',
    },
  },
  variableReference: {
    noVarsForOperation: 'Spremenljivk ni na voljo za dodelitev z izbrano operacijo.',
    conversationVars: 'Spremenljivke pogovora',
    noAssignedVars: 'Ni razpoložljivih dodeljenih spremenljivk',
    noAvailableVars: 'Ni spremenljivk, ki so na voljo',
    assignedVarsDescription: 'Dodeljene spremenljivke morajo biti zapisljive, kot so:',
  },
  versionHistory: {
    filter: {
      all: 'Vse',
      empty: 'Ni najdene zgodovine različic, ki bi se ujemala.',
      onlyShowNamedVersions: 'Prikaži samo poimenovane različice',
      reset: 'Ponastavi filter',
      onlyYours: 'Samo tvoje',
    },
    editField: {
      titleLengthLimit: 'Naslov ne sme presegati {{limit}} znakov',
      title: 'Naslov',
      releaseNotesLengthLimit: 'Opombe o različici ne smejo presegati {{limit}} znakov.',
      releaseNotes: 'Opombe o izdaji',
    },
    action: {
      updateFailure: 'Posodobitev različice ni uspela',
      restoreFailure: 'Obnovitev različice ni uspela',
      updateSuccess: 'Različica posodobljena',
      restoreSuccess: 'Obnovljena različica',
      deleteSuccess: 'Različica izbrisana',
      deleteFailure: 'Brisanje različice ni uspelo',
    },
    releaseNotesPlaceholder: 'Opisujte, kaj se je spremenilo.',
    latest: 'Najnovejši',
    deletionTip: 'Izbris je nepovraten, prosim potrdite.',
    defaultName: 'Nepodpisana različica',
    nameThisVersion: 'Poimenujte to različico',
    restorationTip: 'Po obnovitvi različice bo trenutni osnutek prepisan.',
    currentDraft: 'Trenutni osnutek',
    editVersionInfo: 'Uredi informacije o različici',
    title: 'Različice',
  },
}

export default translation
