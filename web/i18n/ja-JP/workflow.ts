const translation = {
  common: {
    undo: '元に戻す',
    redo: 'やり直し',
    editing: '編集中',
    autoSaved: '自動保存済み',
    unpublished: '未公開',
    published: '公開済み',
    publish: '公開する',
    update: '更新',
    publishUpdate: '更新を公開',
    run: '実行',
    running: '実行中',
    inRunMode: '実行モード中',
    inPreview: 'プレビュー中',
    inPreviewMode: 'プレビューモード中',
    preview: 'プレビュー',
    viewRunHistory: '実行履歴を表示',
    runHistory: '実行履歴',
    goBackToEdit: '編集に戻る',
    conversationLog: '会話ログ',
    features: '機能',
    featuresDescription: 'Webアプリの操作性を向上させる機能',
    ImageUploadLegacyTip: '開始フォームでファイル型変数が作成可能になりました。画像アップロード機能は今後サポート終了となります。',
    fileUploadTip: '画像アップロード機能がファイルアップロードに拡張されました',
    featuresDocLink: '詳細を見る',
    debugAndPreview: 'プレビュー',
    restart: '再起動',
    currentDraft: '現在の下書き',
    currentDraftUnpublished: '現在の下書き（未公開）',
    latestPublished: '最新公開版',
    publishedAt: '公開日時',
    restore: '復元',
    versionHistory: 'バージョン履歴',
    exitVersions: 'バージョン履歴を閉じる',
    runApp: 'アプリを実行',
    batchRunApp: 'アプリを一括実行',
    openInExplore: '探索ページで開く',
    accessAPIReference: 'APIリファレンス',
    embedIntoSite: 'サイトに埋め込む',
    addTitle: 'タイトルを追加...',
    addDescription: '説明を追加...',
    noVar: '変数がありません',
    searchVar: '変数を検索',
    variableNamePlaceholder: '変数名を入力',
    setVarValuePlaceholder: '変数値を設定',
    needConnectTip: '接続されていないステップがあります',
    maxTreeDepth: '1ブランチあたりの最大ノード数：{{depth}}',
    needEndNode: '終了ブロックを追加する必要があります',
    needAnswerNode: '回答ブロックを追加する必要があります',
    workflowProcess: 'ワークフロー処理',
    notRunning: 'まだ実行されていません',
    previewPlaceholder: '入力欄にテキストを入力してチャットボットのデバッグを開始',
    effectVarConfirm: {
      title: '変数の削除',
      content: '他のノードで変数が使用されています。それでも削除しますか？',
    },
    insertVarTip: '"/"キーで変数を挿入',
    processData: 'データ処理',
    input: '入力',
    output: '出力',
    jinjaEditorPlaceholder: '「/」または 「{」で変数挿入',
    viewOnly: '閲覧のみ',
    showRunHistory: '実行履歴を表示',
    enableJinja: 'Jinjaテンプレートを有効化',
    learnMore: '詳細を見る',
    copy: 'コピー',
    duplicate: '複製',
    addBlock: 'ブロックを追加',
    pasteHere: 'ここに貼り付け',
    pointerMode: 'ポインターモード',
    handMode: 'ハンドモード',
    exportImage: '画像を出力',
    exportPNG: 'PNGで出力',
    exportJPEG: 'JPEGで出力',
    exportSVG: 'SVGで出力',
    model: 'モデル',
    workflowAsTool: 'ワークフローをツールとして公開する',
    configureRequired: '設定が必要',
    configure: '設定',
    manageInTools: 'ツールページで管理',
    workflowAsToolTip: 'ワークフロー更新後はツールの再設定が必要です',
    viewDetailInTracingPanel: '詳細を表示',
    syncingData: 'データ同期中。。。',
    importDSL: 'DSLをインポート',
    importDSLTip: '現在の下書きは上書きされます。インポート前にワークフローをエクスポートしてバックアップしてください',
    backupCurrentDraft: '現在の下書きをバックアップ',
    chooseDSL: 'DSL(yml)ファイルを選択',
    overwriteAndImport: '上書きしてインポート',
    importFailure: 'インポート失敗',
    importWarning: '注意事項',
    importWarningDetails: 'DSLバージョンの違いにより機能に影響が出る可能性があります',
    importSuccess: 'インポート成功',
    parallelRun: '並列実行',
    parallelTip: {
      click: {
        title: 'クリック',
        desc: 'で追加',
      },
      drag: {
        title: 'ドラッグ',
        desc: 'で接続',
      },
      limit: '並列処理可能ブランチ数：{{num}}',
      depthLimit: '並列ネスト最大階層数：{{num}}',
    },
    disconnect: '接続解除',
    jumpToNode: 'このノードに移動',
    addParallelNode: '並列ノードを追加',
    parallel: '並列',
    branch: 'ブランチ',
    onFailure: '失敗時',
    addFailureBranch: '失敗ブランチを追加',
    loadMore: 'さらに読み込む',
    noHistory: '履歴がありません',
  },
  env: {
    envPanelTitle: '環境変数',
    envDescription: '環境変数は、個人情報や認証情報を格納するために使用することができます。これらは読み取り専用であり、DSLファイルからエクスポートする際には分離されます。',
    envPanelButton: '環境変数を追加',
    modal: {
      title: '環境変数を追加',
      editTitle: '環境変数を編集',
      type: 'タイプ',
      name: '変数名',
      namePlaceholder: '変数名を入力',
      value: '値',
      valuePlaceholder: '変数値を入力',
      secretTip: 'この変数は機密情報やデータを定義するために使用されます。DSL をエクスポートするときに漏洩防止メカニズムを設定されます。',
    },
    export: {
      title: 'シークレット環境変数をエクスポートしますか？',
      checkbox: 'シークレット値を含む',
      ignore: 'DSLをエクスポート',
      export: 'シークレット値付きでエクスポート',
    },
  },
  chatVariable: {
    panelTitle: '会話変数',
    panelDescription: '対話情報を保存・管理（会話履歴/ファイル/ユーザー設定など）。書き換えができます。',
    docLink: '詳細ドキュメント',
    button: '変数を追加',
    modal: {
      title: '会話変数を追加',
      editTitle: '会話変数を編集',
      name: '変数名',
      namePlaceholder: '変数名を入力',
      type: 'タイプ',
      value: 'デフォルト値',
      valuePlaceholder: 'デフォルト値、設定しない場合は空白にしてください',
      description: '説明',
      descriptionPlaceholder: '変数の説明を入力',
      editInJSON: 'JSONで編集',
      oneByOne: '個別追加',
      editInForm: 'フォームで編集',
      arrayValue: '値',
      addArrayValue: '値を追加',
      objectKey: 'キー',
      objectType: 'タイプ',
      objectValue: 'デフォルト値',
    },
    storedContent: '保存内容',
    updatedAt: '最終更新：',
  },
  changeHistory: {
    title: '変更履歴',
    placeholder: 'まだ何も変更されていません',
    clearHistory: '履歴をクリア',
    hint: 'ヒント',
    hintText: 'エディターでの編集操作は、エディターを離れるまで、お使いのデバイスに記録されます。この履歴は、エディターを離れると消去されます。',
    stepBackward_one: '{{count}} ステップ戻る',
    stepBackward_other: '{{count}} ステップ戻る',
    stepForward_one: '{{count}} ステップ進む',
    stepForward_other: '{{count}} ステップ進む',
    sessionStart: 'セッション開始',
    currentState: '現在の状態',
    nodeTitleChange: 'ブロックのタイトルが変更されました',
    nodeDescriptionChange: 'ブロックの説明が変更されました',
    nodeDragStop: 'ブロックが移動されました',
    nodeChange: 'ブロックが変更されました',
    nodeConnect: 'ブロックが接続されました',
    nodePaste: 'ブロックが貼り付けられました',
    nodeDelete: 'ブロックが削除されました',
    nodeAdd: 'ブロックが追加されました',
    nodeResize: 'ブロックのサイズが変更されました',
    noteAdd: '注釈が追加されました',
    noteChange: '注釈が変更されました',
    noteDelete: '注釈が削除されました',
    edgeDelete: 'ブロックの接続が解除されました',
  },
  errorMsg: {
    fieldRequired: '{{field}} は必須です',
    rerankModelRequired: 'Rerank モデルが設定されていません',
    authRequired: '認証が必要です',
    invalidJson: '{{field}} は無効な JSON です',
    fields: {
      variable: '変数名',
      variableValue: '変数値',
      code: 'コード',
      model: 'モデル',
      rerankModel: 'Rerankモデル',
      visionVariable: 'ビジョン変数',
    },
    invalidVariable: '無効な変数です',
    noValidTool: '{{field}} に利用可能なツールがありません',
    toolParameterRequired: '{{field}}: パラメータ [{{param}}] は必須です',
  },
  singleRun: {
    testRun: 'テスト実行',
    startRun: '実行開始',
    running: '実行中',
    testRunIteration: 'テスト実行（イテレーション）',
    testRunLoop: 'テスト実行（ループ）',
    back: '戻る',
    iteration: 'イテレーション',
    loop: 'ループ',
  },
  tabs: {
    'searchBlock': 'ブロック検索',
    'blocks': 'ブロック',
    'searchTool': 'ツール検索',
    'tools': 'ツール',
    'allTool': 'すべて',
    'customTool': 'カスタム',
    'workflowTool': 'ワークフロー',
    'question-understand': '問題理解',
    'logic': 'ロジック',
    'transform': '変換',
    'utilities': 'ツール',
    'noResult': '該当なし',
    'plugin': 'プラグイン',
    'agent': 'エージェント戦略',
  },
  blocks: {
    'start': '開始',
    'end': '終了',
    'answer': '回答',
    'llm': 'LLM',
    'knowledge-retrieval': '知識検索',
    'question-classifier': '質問分類器',
    'if-else': 'IF/ELSE',
    'code': 'コード実行',
    'template-transform': 'テンプレート',
    'http-request': 'HTTPリクエスト',
    'variable-assigner': '変数代入器',
    'variable-aggregator': '変数集約器',
    'assigner': '変数代入',
    'iteration-start': 'イテレーション開始',
    'iteration': 'イテレーション',
    'parameter-extractor': 'パラメータ抽出',
    'document-extractor': 'テキスト抽出',
    'list-operator': 'リスト処理',
    'agent': 'エージェント',
    'loop-start': 'ループ開始',
    'loop': 'ループ',
    'loop-end': 'ループ完了',
  },
  blocksAbout: {
    'start': 'ワークフロー開始時の初期パラメータを定義します。',
    'end': 'ワークフローの終了条件と結果のタイプを定義します。',
    'answer': 'チャットダイアログの返答内容を定義します。',
    'llm': '大規模言語モデルを呼び出して質問回答や自然言語処理を実行します。',
    'knowledge-retrieval': 'ナレッジベースからユーザー質問に関連するテキストを検索します。',
    'question-classifier': '質問の分類条件を定義し、LLMが分類に基づいて対話フローを制御します。',
    'if-else': 'if/else条件でワークフローを2つの分岐に分割します。',
    'code': 'Python/NodeJSコードを実行してカスタムロジックを実装します。',
    'template-transform': 'Jinjaテンプレート構文でデータを文字列に変換します。',
    'http-request': 'HTTPリクエストを送信できます。',
    'variable-assigner': '複数分岐の変数を集約し、下流ノードの設定を統一します。',
    'assigner': '書き込み可能な変数（例：会話変数）への値の割り当てを行います。',
    'variable-aggregator': '複数分岐の変数を集約し、下流ノードの設定を統一します。',
    'iteration': 'リスト要素に対して反復処理を実行し全結果を出力します。',
    'loop': '終了条件達成まで、または最大反復回数までロジックを繰り返します。',
    'loop-end': '「break」相当の機能です。このノードに設定項目はなく、ループ処理中にこのノードに到達すると即時終了します。',
    'parameter-extractor': '自然言語から構造化パラメータを抽出し、後続処理で利用します。',
    'document-extractor': 'アップロード文書をLLM処理用に最適化されたテキストに変換します。',
    'list-operator': '配列のフィルタリングやソート処理を行います。',
    'agent': '大規模言語モデルを活用した質問応答や自然言語処理を実行します。',
  },
  operator: {
    zoomIn: '拡大',
    zoomOut: '縮小',
    zoomTo50: '50%サイズ',
    zoomTo100: '等倍表示',
    zoomToFit: '画面に合わせる',
  },
  variableReference: {
    noAvailableVars: '利用可能な変数がありません',
    noVarsForOperation: 'この操作に割り当て可能な変数が存在しません。',
    noAssignedVars: '割り当て可能な変数がありません',
    assignedVarsDescription: '書き込み可能な変数（例：',
    conversationVars: '会話変数',
  },
  panel: {
    userInputField: 'ユーザー入力欄',
    changeBlock: 'ノード変更',
    helpLink: 'ヘルプリンク',
    about: '詳細',
    createdBy: '作成者',
    nextStep: '次のステップ',
    addNextStep: 'このワークフローで次ノードを追加',
    selectNextStep: '次ノード選択',
    runThisStep: 'このステップ実行',
    checklist: 'チェックリスト',
    checklistTip: '公開前に全ての項目を確認してください',
    checklistResolved: '全てのチェックが完了しました',
    organizeBlocks: 'ノード整理',
    change: '変更',
    optional: '（任意）',
    moveToThisNode: 'このノードに移動する',
  },
  nodes: {
    common: {
      outputVars: '出力変数',
      insertVarTip: '変数を挿入',
      memory: {
        memory: 'メモリ',
        memoryTip: 'チャットメモリ設定',
        windowSize: 'メモリウィンドウサイズ',
        conversationRoleName: '会話ロール名',
        user: 'ユーザー接頭辞',
        assistant: 'アシスタント接頭辞',
      },
      memories: {
        title: 'メモリ',
        tip: 'チャットの記憶管理',
        builtIn: '組み込み',
      },
      errorHandle: {
        title: '例外処理',
        tip: 'ノード例外発生時の処理ポリシーを設定',
        none: {
          title: '処理なし',
          desc: '例外発生時に処理を停止',
        },
        defaultValue: {
          title: 'デフォルト値',
          desc: '例外発生時のデフォルト出力',
          tip: '例外発生時に返される値:',
          inLog: 'ノード例外 - デフォルト値を出力',
          output: 'デフォルト値出力',
        },
        failBranch: {
          title: '例外分岐',
          desc: '例外発生時に分岐を実行',
          customize: '失敗分岐ロジックをカスタマイズ',
          customizeTip: '例外発生時、失敗分岐でエラー処理を柔軟に設定可能（エラーログ表示/修復処理/操作スキップ等）',
          inLog: 'ノード例外 - 失敗分岐を実行。エラー情報を下流に伝播',
        },
        partialSucceeded: {
          tip: '{{num}}個のノードで異常発生。ログはトレース画面で確認可能',
        },
      },
      retry: {
        retry: '再試行',
        retryOnFailure: '失敗時再試行',
        maxRetries: '最大試行回数',
        retryInterval: '再試行間隔',
        retryTimes: '失敗時 {{times}}回再試行',
        retrying: '再試行中...',
        retrySuccessful: '再試行成功',
        retryFailed: '再試行失敗',
        retryFailedTimes: '{{times}}回再試行失敗',
        times: '回',
        ms: 'ミリ秒',
        retries: '再試行回数: {{num}}',
      },
    },
    start: {
      required: '必須',
      inputField: '入力フィールド',
      builtInVar: '組み込み変数',
      outputVars: {
        query: 'ユーザー入力',
        memories: {
          des: '会話履歴',
          type: 'メッセージ種別',
          content: 'メッセージ内容',
        },
        files: 'ファイル一覧',
      },
      noVarTip: '入力設定はワークフロー内で利用可能',
    },
    end: {
      outputs: '出力設定',
      output: {
        type: '出力形式',
        variable: '出力変数',
      },
      type: {
        'none': 'なし',
        'plain-text': 'プレーンテキスト',
        'structured': '構造化',
      },
    },
    answer: {
      answer: '応答',
      outputVars: '出力変数',
    },
    llm: {
      model: 'AIモデル',
      variables: '変数',
      context: 'コンテキスト',
      contextTooltip: 'ナレッジベースをコンテキストとして利用',
      notSetContextInPromptTip: 'コンテキスト利用時はプロンプトに変数を明記してください',
      prompt: 'プロンプト',
      addMessage: 'メッセージ追加',
      roleDescription: {
        system: '対話の基本動作を定義',
        user: '指示/質問を入力',
        assistant: 'ユーザー入力への応答',
      },
      vision: 'ビジョン',
      files: 'ファイル',
      resolution: {
        name: '解像度',
        high: '高',
        low: '低',
      },
      outputVars: {
        output: '生成内容',
        usage: 'モデル使用量',
      },
      singleRun: {
        variable: '変数',
      },
      sysQueryInUser: 'ユーザーメッセージにsys.queryを含めてください',
      jsonSchema: {
        title: '構造化データスキーマ',
        instruction: '指示',
        promptTooltip: 'テキスト説明から標準JSONスキーマを自動生成できます。',
        promptPlaceholder: 'JSONスキーマを入力...',
        generate: '生成',
        import: 'JSONインポート',
        generateJsonSchema: 'スキーマ生成',
        generationTip: '自然言語で簡単にJSONスキーマを作成可能です。',
        generating: 'JSONスキーマを生成中...',
        generatedResult: '生成結果',
        resultTip: 'こちらが生成された結果です。ご満足いただけない場合は、前の画面に戻ってプロンプトを修正できます。',
        back: '前に戻る',
        regenerate: '再生成する',
        apply: '適用',
        doc: '構造化出力の詳細を見る',
        resetDefaults: '初期化',
        required: '必須項目',
        addField: 'フィールドを追加',
        addChildField: 'サブフィールドを追加',
        showAdvancedOptions: '詳細設定',
        stringValidations: '文字列検証',
        fieldNamePlaceholder: 'フィールド名',
        descriptionPlaceholder: '説明を入力',
        warningTips: {
          saveSchema: '編集中のフィールドを確定してから保存してください。',
        },
      },
    },
    knowledgeRetrieval: {
      queryVariable: '検索変数',
      knowledge: 'ナレッジベース',
      outputVars: {
        output: '検索結果セグメント',
        content: 'セグメント内容',
        title: 'セグメントタイトル',
        icon: 'セグメントアイコン',
        url: 'セグメントURL',
        metadata: 'メタデータ',
      },
      metadata: {
        title: 'メタデータフィルタ',
        tip: 'タグ/カテゴリ等の属性で検索を絞り込み',
        options: {
          disabled: {
            title: '無効',
            subTitle: 'フィルタリング不使用',
          },
          automatic: {
            title: '自動生成',
            subTitle: '検索履歴からフィルタ条件を自動生成',
            desc: 'Query Variable（検索変数）に基づきフィルタ条件を自動生成',
          },
          manual: {
            title: '手動設定',
            subTitle: 'メタデータの条件を手動で追加',
          },
        },
        panel: {
          title: 'メタデータのフィルタ条件',
          conditions: '条件一覧',
          add: '条件追加',
          search: 'メタデータ検索',
          placeholder: '値を入力',
          datePlaceholder: '日付選択...',
          select: '変数選択...',
        },
      },
    },
    http: {
      inputVars: '入力変数',
      api: 'API',
      apiPlaceholder: 'URLを入力（変数使用時は"/"を入力）',
      extractListPlaceholder: 'リスト番号を入力（変数使用時は"/"を入力）',
      notStartWithHttp: 'APIは http:// または https:// で始まってください',
      key: 'キー',
      type: 'タイプ',
      value: '値',
      bulkEdit: '一括編集',
      keyValueEdit: 'キーバリュー編集',
      headers: 'ヘッダー',
      params: 'パラメータ',
      body: 'ボディ',
      binaryFileVariable: 'バイナリファイル変数',
      outputVars: {
        body: 'レスポンスコンテンツ',
        statusCode: 'レスポンスステータスコード',
        headers: 'レスポンスヘッダ（JSON）',
        files: 'ファイル一覧',
      },
      authorization: {
        'authorization': '認証',
        'authorizationType': '認証タイプ',
        'no-auth': 'なし',
        'api-key': 'APIキー',
        'auth-type': 'API認証タイプ',
        'basic': 'ベーシック',
        'bearer': 'Bearer',
        'custom': 'カスタム',
        'api-key-title': 'APIキー',
        'header': 'ヘッダー',
      },
      insertVarPlaceholder: '変数を挿入するには\'/\'を入力してください',
      timeout: {
        title: 'タイムアウト設定',
        connectLabel: '接続タイムアウト',
        connectPlaceholder: '接続タイムアウト（秒）',
        readLabel: '読み取りタイムアウト',
        readPlaceholder: '読み取りタイムアウト（秒）',
        writeLabel: '書き込みタイムアウト',
        writePlaceholder: '書き込みタイムアウト（秒）',
      },
      curl: {
        title: 'cURLからインポート',
        placeholder: 'ここにcURL文字列を貼り付けます',
      },
    },
    code: {
      inputVars: '入力変数',
      outputVars: '出力変数',
      advancedDependencies: '高度な依存関係',
      advancedDependenciesTip: '消費に時間がかかる、またはデフォルトで組み込まれていない事前ロードされた依存関係を追加します',
      searchDependencies: '依存関係を検索',
    },
    templateTransform: {
      inputVars: '入力変数',
      code: 'コード',
      codeSupportTip: 'Jinja2のみをサポートしています',
      outputVars: {
        output: '変換されたコンテンツ',
      },
    },
    ifElse: {
      if: 'もし',
      else: 'それ以外',
      elseDescription: 'IF条件が満たされない場合に実行するロジックを定義します。',
      and: 'かつ',
      or: 'または',
      operator: '演算子',
      notSetVariable: 'まず変数を設定してください',
      comparisonOperator: {
        'contains': '含む',
        'not contains': '含まない',
        'start with': 'で始まる',
        'end with': 'で終わる',
        'is': 'である',
        'is not': 'でない',
        'empty': '空',
        'not empty': '空でない',
        'null': 'null',
        'not null': 'nullでない',
        'regex match': '正規表現マッチ',
        'in': '含まれている',
        'not in': '含まれていない',
        'all of': 'すべての',
        'exists': '存在します',
        'not exists': '存在しません',
        'before': '前に',
        'after': '後',
      },
      enterValue: '値を入力',
      addCondition: '条件を追加',
      conditionNotSetup: '条件が設定されていません',
      selectVariable: '変数を選択...',
      optionName: {
        audio: '音声',
        localUpload: 'ローカルアップロード',
        image: '画像',
        video: '映像',
        doc: 'ドキュメント',
        url: 'URL',
      },
      select: '選ぶ',
      addSubVariable: 'サブ変数',
    },
    variableAssigner: {
      title: '変数を代入する',
      outputType: '出力タイプ',
      outputVarType: '出力変数のタイプ',
      varNotSet: '変数が設定されていません',
      noVarTip: '代入された変数を追加してください',
      type: {
        string: '文字列',
        number: '数値',
        object: 'オブジェクト',
        array: '配列',
      },
      aggregationGroup: 'グループ',
      aggregationGroupTip: 'この機能を有効にすると、変数集約器は複数のセットの変数を集約できます。',
      addGroup: 'グループを追加',
      outputVars: {
        varDescribe: '{{groupName}} 出力',
      },
      setAssignVariable: '代入された変数を設定',
    },
    assigner: {
      'assignedVariable': '代入された変数',
      'writeMode': '書き込みモード',
      'writeModeTip': '代入された変数が配列の場合, 末尾に追記モードを追加する。',
      'over-write': '上書き',
      'append': '追記',
      'plus': 'プラス',
      'clear': 'クリア',
      'setVariable': '変数を設定する',
      'variable': '変数',
      'operations': {
        'title': '操作',
        'set': 'セット',
        'clear': 'クリア',
        'overwrite': '上書き',
        'append': '追加',
        '-=': '-=',
        '/=': '/=',
        '+=': '+=',
        'over-write': '上書き',
        'extend': '延ばす',
        '*=': '*=',
        'remove-last': '最後を削除する',
        'remove-first': '最初を削除する',
      },
      'setParameter': 'パラメータを設定...',
      'selectAssignedVariable': '代入変数を選択...',
      'varNotSet': '変数が設定されていません',
      'variables': '変数',
      'noVarTip': '「+」ボタンをクリックして変数を追加します',
      'noAssignedVars': '使用可能な代入変数がありません',
      'assignedVarsDescription': '代入される変数は、会話変数などの書き込み可能な変数である必要があります。',
    },
    tool: {
      toAuthorize: '承認するには',
      inputVars: '入力変数',
      outputVars: {
        text: 'ツールが生成したコンテンツ',
        files: {
          title: 'ツールが生成したファイル',
          type: 'サポートタイプ。現在は画像のみサポートされています',
          transfer_method: '転送方法。値はremote_urlまたはlocal_fileです',
          url: '画像URL',
          upload_file_id: 'アップロードファイルID',
        },
        json: 'ツールで生成されたJSON',
      },
    },
    questionClassifiers: {
      model: 'モデル',
      inputVars: '入力変数',
      outputVars: {
        className: 'クラス名',
      },
      class: 'クラス',
      classNamePlaceholder: 'クラス名を入力してください',
      advancedSetting: '高度な設定',
      topicName: 'トピック名',
      topicPlaceholder: 'トピック名を入力してください',
      addClass: 'クラスを追加',
      instruction: '指示',
      instructionTip: '質問分類器が質問をどのように分類するかをよりよく理解するための追加の指示を入力します。',
      instructionPlaceholder: '指示を入力してください',
    },
    parameterExtractor: {
      inputVar: '入力変数',
      extractParameters: 'パラメーターを抽出',
      importFromTool: 'ツールからインポート',
      addExtractParameter: '抽出パラメーターを追加',
      addExtractParameterContent: {
        name: '名前',
        namePlaceholder: '抽出パラメーター名',
        type: 'タイプ',
        typePlaceholder: '抽出パラメータータイプ',
        description: '説明',
        descriptionPlaceholder: '抽出パラメーターの説明',
        required: '必須',
        requiredContent: '必須はモデル推論の参考としてのみ使用され、パラメーター出力の必須検証には使用されません。',
      },
      extractParametersNotSet: '抽出パラメーターが設定されていません',
      instruction: '指示',
      instructionTip: 'パラメーター抽出器がパラメーターを抽出する方法を理解するのに役立つ追加の指示を入力します。',
      advancedSetting: '高度な設定',
      reasoningMode: '推論モード',
      reasoningModeTip: '関数呼び出しやプロンプトの指示に応答するモデルの能力に基づいて、適切な推論モードを選択できます。',
      isSuccess: '成功。成功した場合の値は1、失敗した場合の値は0です。',
      errorReason: 'エラーの理由',
    },
    iteration: {
      deleteTitle: 'イテレーションノードを削除しますか？',
      deleteDesc: 'イテレーションノードを削除すると、すべての子ノードが削除されます',
      input: '入力',
      output: '出力変数',
      iteration_one: '{{count}} イテレーション',
      iteration_other: '{{count}} イテレーション',
      currentIteration: '現在のイテレーション',
      ErrorMethod: {
        operationTerminated: '終了',
        continueOnError: 'エラー時に続行',
        removeAbnormalOutput: 'アブノーマルアウトプットの削除',
      },
      comma: ',',
      error_other: '{{カウント}}エラー',
      error_one: '{{カウント}}エラー',
      parallelModeUpper: 'パラレルモード',
      parallelMode: 'パラレルモード',
      MaxParallelismTitle: '最大並列処理',
      errorResponseMethod: 'エラー応答方式',
      parallelPanelDesc: '並列モードでは、イテレーションのタスクは並列実行をサポートします。',
      parallelModeEnableDesc: '並列モードでは、イテレーション内のタスクは並列実行をサポートします。これは、右側のプロパティパネルで構成できます。',
      parallelModeEnableTitle: 'パラレルモード有効',
      MaxParallelismDesc: '最大並列処理は、1 回の反復で同時に実行されるタスクの数を制御するために使用されます。',
      answerNodeWarningDesc: '並列モードの警告: 応答ノード、会話変数の割り当て、およびイテレーション内の永続的な読み取り/書き込み操作により、例外が発生する可能性があります。',
    },
    loop: {
      deleteTitle: 'ループノードを削除しますか？',
      deleteDesc: 'ループノードを削除すると、全ての子ノードが削除されます。',
      input: '入力',
      output: '出力変数',
      loop_one: '{{count}}回',
      loop_other: '{{count}}回',
      currentLoop: '現在のループ',
      breakCondition: 'ループ終了条件',
      breakConditionTip: 'ループ内の変数やセッション変数を参照し、終了条件を設定できます。',
      loopMaxCount: '最大ループ回数',
      loopMaxCountError: '最大ループ回数は1から{{maxCount}}の範囲で正しく入力してください。',
      errorResponseMethod: 'エラー対応方法',
      ErrorMethod: {
        operationTerminated: 'エラー時に処理を終了',
        continueOnError: 'エラーを無視して継続',
        removeAbnormalOutput: '異常出力を除外',
      },
      loopVariables: 'ループ変数',
      initialLoopVariables: '初期ループ変数',
      finalLoopVariables: '最終ループ変数',
      setLoopVariables: 'ループスコープ内で変数を設定',
      variableName: '変数名',
      inputMode: '入力モード',
      exitConditionTip: 'ループノードには少なくとも1つの終了条件が必要です',
      loopNode: 'ループノード',
      currentLoopCount: '現在のループ回数: {{count}}',
      totalLoopCount: '総ループ回数: {{count}}',
      error_other: '{{count}} エラー',
      error_one: '{{count}} エラー',
      comma: ',',
    },
    note: {
      addNote: 'コメントを追加',
      editor: {
        placeholder: 'メモを書く...',
        small: '小',
        medium: '中',
        large: '大',
        bold: '太字',
        italic: '斜体',
        strikethrough: '打ち消し線',
        link: 'リンク',
        openLink: '開く',
        unlink: 'リンクをキャンセル',
        enterUrl: 'リンク入力中...',
        invalidUrl: 'リンク無効',
        bulletList: 'リスト',
        showAuthor: '著者を表示する',
      },
    },
    docExtractor: {
      outputVars: {
        text: '抽出されたテキスト',
      },
      inputVar: '入力変数',
      learnMore: '詳細はこちら',
      supportFileTypes: 'サポートするファイルタイプ: {{types}}。',
    },
    listFilter: {
      outputVars: {
        last_record: '最後のレコード',
        first_record: '最初のレコード',
        result: 'フィルター結果',
      },
      limit: 'トップN',
      asc: 'ASC',
      filterCondition: 'フィルター条件',
      filterConditionKey: 'フィルター条件キー',
      orderBy: '並べる順番',
      filterConditionComparisonValue: 'フィルター条件の値',
      selectVariableKeyPlaceholder: 'サブ変数キーを選択する',
      filterConditionComparisonOperator: 'フィルター条件を比較オペレーター',
      inputVar: '入力変数',
      desc: 'DESC',
      extractsCondition: 'N個のアイテムを抽出します',
    },
    agent: {
      strategy: {
        label: 'エージェンティック戦略',
        configureTipDesc: 'エージェント戦略を設定した後、このノードは残りの設定を自動的に読み込みます。この戦略は、マルチステップツール推論のメカニズムに影響を与えます。',
        searchPlaceholder: 'エージェンティック戦略を検索する',
        configureTip: 'エージェンティック戦略を設定してください。',
        shortLabel: '戦略',
        tooltip: '異なるエージェンティック戦略が、システムがマルチステップのツール呼び出しを計画し実行する方法を決定します。',
        selectTip: 'エージェンシー戦略を選択する',
      },
      pluginInstaller: {
        install: 'インストール',
        installing: 'インストール中',
      },
      modelNotInMarketplace: {
        manageInPlugins: 'プラグインを管理する',
        title: 'モデルがインストールされていません',
        desc: 'このモデルはローカルまたはGitHubリポジトリからインストールされます。インストール後にご利用ください。',
      },
      modelNotSupport: {
        title: 'サポートされていないモデル',
        descForVersionSwitch: 'インストールされたプラグインのバージョンはこのモデルを提供していません。バージョンを切り替えるにはクリックしてください。',
        desc: 'インストールされたプラグインのバージョンは、このモデルを提供していません。',
      },
      modelSelectorTooltips: {
        deprecated: 'このモデルは廃止されました',
      },
      outputVars: {
        files: {
          url: '画像のURL',
          type: 'サポートタイプ。現在はサポート画像のみ',
          upload_file_id: 'ファイルIDをアップロード',
          transfer_method: '転送方法。値はremote_urlまたはlocal_fileです。',
          title: 'エージェント生成ファイル',
        },
        text: 'エージェント生成コンテンツ',
        json: 'エージェント生成のJSON',
      },
      checkList: {
        strategyNotSelected: '戦略が選択されていません',
      },
      installPlugin: {
        install: 'インストール',
        changelog: '変更ログ',
        cancel: 'キャンセル',
        desc: '次のプラグインをインストールしようとしています',
        title: 'プラグインをインストールする',
      },
      strategyNotSet: 'エージェンティック戦略は設定されていません',
      strategyNotInstallTooltip: '{{strategy}}はインストールされていません',
      modelNotSelected: 'モデルが選択されていません',
      toolNotAuthorizedTooltip: '{{tool}} 認可されていません',
      toolNotInstallTooltip: '{{tool}}はインストールされていません',
      tools: '道具',
      learnMore: 'もっと学ぶ',
      configureModel: 'モデルを設定する',
      model: 'モデル',
      linkToPlugin: 'プラグインへのリンク',
      notAuthorized: '権限がありません',
      modelNotInstallTooltip: 'このモデルはインストールされていません',
      maxIterations: '最大反復回数',
      toolbox: 'ツールボックス',
      pluginNotInstalled: 'このプラグインはインストールされていません',
      strategyNotFoundDescAndSwitchVersion: 'インストールされたプラグインのバージョンはこの戦略を提供していません。バージョンを切り替えるにはクリックしてください。',
      pluginNotInstalledDesc: 'このプラグインはGitHubからインストールされています。再インストールするにはプラグインに移動してください。',
      unsupportedStrategy: 'サポートされていない戦略',
      pluginNotFoundDesc: 'このプラグインはGitHubからインストールされています。再インストールするにはプラグインに移動してください。',
      strategyNotFoundDesc: 'インストールされたプラグインのバージョンは、この戦略を提供していません。',
    },
  },
  tracing: {
    stopBy: '{{user}}によって停止',
  },
  versionHistory: {
    title: 'バージョン',
    currentDraft: '現在の下書き',
    latest: '最新版',
    filter: {
      all: 'すべて',
      onlyYours: '自分のみ',
      onlyShowNamedVersions: '名前付きバージョンのみ',
      reset: 'リセット',
      empty: '該当するバージョンがありません',
    },
    defaultName: '名称未設定',
    nameThisVersion: 'バージョン名を付ける',
    editVersionInfo: 'バージョン情報を編集',
    editField: {
      title: 'タイトル',
      releaseNotes: 'リリースノート',
      titleLengthLimit: 'タイトルは{{limit}}文字以内で入力してください',
      releaseNotesLengthLimit: 'リリースノートは{{limit}}文字以内で入力してください',
    },
    releaseNotesPlaceholder: '変更内容を入力してください',
    restorationTip: 'バージョンを復元すると、現在の下書きが上書きされます',
    deletionTip: '削除したデータは復元できません。よろしいですか？',
    action: {
      restoreSuccess: '復元が完了しました',
      restoreFailure: '復元に失敗しました',
      deleteSuccess: '削除が完了しました',
      deleteFailure: '削除に失敗しました',
      updateSuccess: '更新が完了しました',
      updateFailure: '更新に失敗しました',
    },
  },
}

export default translation
