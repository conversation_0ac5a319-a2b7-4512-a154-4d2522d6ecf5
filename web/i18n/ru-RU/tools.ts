const translation = {
  title: 'Инструменты',
  createCustomTool: 'Создать пользовательский инструмент',
  customToolTip: 'Узнать больше о пользовательских инструментах Dify',
  type: {
    all: 'Все',
    builtIn: 'Встроенные',
    custom: 'Пользовательские',
    workflow: 'Рабочий процесс',
  },
  contribute: {
    line1: 'Я заинтересован в',
    line2: 'внесении инструментов в Dify.',
    viewGuide: 'Посмотреть руководство',
  },
  author: 'Автор',
  auth: {
    unauthorized: 'Авторизовать',
    authorized: 'Авторизовано',
    setup: 'Настроить авторизацию для использования',
    setupModalTitle: 'Настроить авторизацию',
    setupModalTitleDescription: 'После настройки учетных данных все участники рабочего пространства смогут использовать этот инструмент при оркестровке приложений.',
  },
  includeToolNum: 'Включено {{num}} инструментов',
  addTool: 'Добавить инструмент',
  addToolModal: {
    type: 'тип',
    category: 'категория',
    add: 'добавить',
    added: 'добавлено',
    manageInTools: 'Управлять в инструментах',
    emptyTitle: 'Нет доступных инструментов рабочего процесса',
    emptyTip: 'Перейдите в "Рабочий процесс -> Опубликовать как инструмент"',
    emptyTitleCustom: 'Нет пользовательского инструмента',
    emptyTipCustom: 'Создание пользовательского инструмента',
  },
  createTool: {
    title: 'Создать пользовательский инструмент',
    editAction: 'Настроить',
    editTitle: 'Редактировать пользовательский инструмент',
    name: 'Название',
    toolNamePlaceHolder: 'Введите название инструмента',
    nameForToolCall: 'Название вызова инструмента',
    nameForToolCallPlaceHolder: 'Используется для машинного распознавания, например getCurrentWeather, list_pets',
    nameForToolCallTip: 'Поддерживаются только цифры, буквы и подчеркивания.',
    description: 'Описание',
    descriptionPlaceholder: 'Краткое описание назначения инструмента, например, получить температуру для определенного местоположения.',
    schema: 'Схема',
    schemaPlaceHolder: 'Введите свою схему OpenAPI здесь',
    viewSchemaSpec: 'Посмотреть спецификацию OpenAPI-Swagger',
    importFromUrl: 'Импортировать из URL',
    importFromUrlPlaceHolder: 'https://...',
    urlError: 'Пожалуйста, введите действительный URL',
    examples: 'Примеры',
    exampleOptions: {
      json: 'Погода (JSON)',
      yaml: 'Зоомагазин (YAML)',
      blankTemplate: 'Пустой шаблон',
    },
    availableTools: {
      title: 'Доступные инструменты',
      name: 'Название',
      description: 'Описание',
      method: 'Метод',
      path: 'Путь',
      action: 'Действия',
      test: 'Тест',
    },
    authMethod: {
      title: 'Метод авторизации',
      type: 'Тип авторизации',
      keyTooltip: 'Ключ заголовка HTTP, вы можете оставить его как "Authorization", если не знаете, что это такое, или установить его на пользовательское значение',
      types: {
        none: 'Нет',
        api_key: 'Ключ API',
        apiKeyPlaceholder: 'Название заголовка HTTP для ключа API',
        apiValuePlaceholder: 'Введите ключ API',
      },
      key: 'Ключ',
      value: 'Значение',
    },
    authHeaderPrefix: {
      title: 'Тип авторизации',
      types: {
        basic: 'Базовый',
        bearer: 'Bearer',
        custom: 'Пользовательский',
      },
    },
    privacyPolicy: 'Политика конфиденциальности',
    privacyPolicyPlaceholder: 'Пожалуйста, введите политику конфиденциальности',
    toolInput: {
      title: 'Входные данные инструмента',
      name: 'Название',
      required: 'Обязательно',
      method: 'Метод',
      methodSetting: 'Настройка',
      methodSettingTip: 'Пользователь заполняет конфигурацию инструмента',
      methodParameter: 'Параметр',
      methodParameterTip: 'LLM заполняет во время вывода',
      label: 'Теги',
      labelPlaceholder: 'Выберите теги (необязательно)',
      description: 'Описание',
      descriptionPlaceholder: 'Описание значения параметра',
    },
    customDisclaimer: 'Пользовательский отказ от ответственности',
    customDisclaimerPlaceholder: 'Пожалуйста, введите пользовательский отказ от ответственности',
    confirmTitle: 'Подтвердить сохранение?',
    confirmTip: 'Приложения, использующие этот инструмент, будут затронуты',
    deleteToolConfirmTitle: 'Удалить этот инструмент?',
    deleteToolConfirmContent: 'Удаление инструмента необратимо. Пользователи больше не смогут получить доступ к вашему инструменту.',
  },
  test: {
    title: 'Тест',
    parametersValue: 'Параметры и значение',
    parameters: 'Параметры',
    value: 'Значение',
    testResult: 'Результаты теста',
    testResultPlaceholder: 'Результат теста будет отображаться здесь',
  },
  thought: {
    using: 'Использование',
    used: 'Использовано',
    requestTitle: 'Запрос к',
    responseTitle: 'Ответ от',
  },
  setBuiltInTools: {
    info: 'Информация',
    setting: 'Настройка',
    toolDescription: 'Описание инструмента',
    parameters: 'параметры',
    string: 'строка',
    number: 'число',
    required: 'Обязательно',
    infoAndSetting: 'Информация и настройки',
    file: 'файл',
  },
  noCustomTool: {
    title: 'Нет пользовательских инструментов!',
    content: 'Добавьте и управляйте своими пользовательскими инструментами здесь для создания приложений ИИ.',
    createTool: 'Создать инструмент',
  },
  noSearchRes: {
    title: 'Извините, результаты не найдены!',
    content: 'Мы не смогли найти никаких инструментов, соответствующих вашему поиску.',
    reset: 'Сбросить поиск',
  },
  builtInPromptTitle: 'Подсказка',
  toolRemoved: 'Инструмент удален',
  notAuthorized: 'Инструмент не авторизован',
  howToGet: 'Как получить',
  openInStudio: 'Открыть в Studio',
  toolNameUsageTip: 'Название вызова инструмента для рассуждений агента и подсказок',
  copyToolName: 'Копировать имя',
  noTools: 'Инструменты не найдены',
}

export default translation
